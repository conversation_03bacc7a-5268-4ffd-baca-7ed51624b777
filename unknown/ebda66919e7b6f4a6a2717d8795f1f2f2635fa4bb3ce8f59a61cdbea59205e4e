'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser, getCurrentUserCompanies, fetchUserData, logout as authLogout, getDashboardPathByRole } from '@/lib/auth';

interface Company {
  company_id: string;
  company_name: string;
  database_name?: string;
}

interface User {
  employee_id: string;
  employee_info: {
    created_at: string;
    department_id: string;
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string | null;
    last_name: string;
    phone_number: string | null;
    position: string | null;
    status: string;
    updated_at: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  companies: Company[];
  setCompanies: (companies: Company[]) => void;
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => void;
  redirectToDashboard: () => void;
  registerCompany: (companyData: { company_name: string; company_tin: string; phone_number?: string }) => Promise<any>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  setUser: () => {},
  companies: [],
  setCompanies: () => {},
  isAuthenticated: false,
  isLoading: true,
  logout: () => {},
  redirectToDashboard: () => {},
  registerCompany: async () => ({}),
  refreshUserData: async () => {},
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is authenticated on mount and get companies
    const initializeAuth = () => {
      if (typeof window === 'undefined') return;

      try {
        // Get user and companies from auth utilities
        const currentUser = getCurrentUser();
        const userCompanies = getCurrentUserCompanies();

        // Set state
        setUser(currentUser);
        setCompanies(userCompanies);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing auth data:', error);
        setUser(null);
        setCompanies([]);
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Set up storage event listener to handle auth changes in other tabs/windows
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'kazisync_auth' || event.key === null) {
        initializeAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const logout = () => {
    authLogout();
    setUser(null);
    setCompanies([]);
    router.replace('/login');
  };

  // Prefetch all dashboard routes on context initialization
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Prefetch all possible routes
      const routesToPrefetch = [
        '/login',
        '/dashboard',
        '/dashboard/super-admin',
        '/dashboard/admin',
        '/dashboard/hr',
        '/dashboard/manager',
        '/dashboard/employee'
      ];

      routesToPrefetch.forEach(route => {
        router.prefetch(route);
      });
    }
  }, [router]);

  const redirectToDashboard = () => {
    const currentUser = getCurrentUser();

    if (currentUser) {
      const dashboardPath = getDashboardPathByRole(currentUser.role);
      // Use replace instead of push to avoid adding to history stack
      router.replace(dashboardPath);
    } else {
      router.replace('/login');
    }
  };

  // Define the company registration response interface
  interface CompanyRegistrationResponse {
    assignment_details: string;
    company: {
      company_id: string;
      company_name: string;
      company_tin: string;
      created_at: string;
      database_name: string;
      devices: any[];
      phone_number: string | null;
    };
    message: string;
    user_assigned: boolean;
  }

  // Function to register a company
  const registerCompany = async (companyData: { company_name: string; company_tin: string; phone_number?: string }) => {
    try {
      const { createApiUrl } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');

      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const url = createApiUrl('add_company');
      console.log(`Making POST request to: ${url}`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(companyData)
      });

      console.log(`Response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.company) {
        const newCompany: Company = {
          company_id: data.company.company_id,
          company_name: data.company.company_name,
          database_name: data.company.database_name
        };
        setCompanies(prev => [...prev, newCompany]);
      }

      return data;
    } catch (error) {
      console.error('Error registering company:', error);
      throw error;
    }
  };

  // Function to refresh user data from the API
  const refreshUserData = async (): Promise<void> => {
    if (!user) {
      console.warn('Cannot refresh user data: No user is logged in');
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetchUserData(user.id);

      if (response.success && response.user) {
        // Update companies state
        setCompanies(response.user.companies);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        companies,
        setCompanies,
        isAuthenticated: !!user,
        isLoading,
        logout,
        redirectToDashboard,
        registerCompany,
        refreshUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
