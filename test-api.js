// Simple test script to check API endpoints
const API_BASE = 'https://sms.remmittance.com';

// Test the statistics endpoint
async function testStatisticsEndpoint() {
  const employeeId = '824fd181-3d17-41ef-8a93-b9d383530822';
  const companyId = 'e7d11f4e-3112-4e39-8619-5c7aaeac6554';
  const url = `${API_BASE}/api/attendance/employee/${employeeId}/statistics?company_id=${companyId}&period=weekly`;
  
  console.log('Testing URL:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: This would need a real auth token in practice
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', data);
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Fetch error:', error);
  }
}

// Test the attendance records endpoint
async function testAttendanceRecordsEndpoint() {
  const employeeId = '824fd181-3d17-41ef-8a93-b9d383530822';
  const companyId = 'e7d11f4e-3112-4e39-8619-5c7aaeac6554';
  const url = `${API_BASE}/api/attendance/employee/${employeeId}?company_id=${companyId}&limit=50`;
  
  console.log('Testing URL:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', data);
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Fetch error:', error);
  }
}

// Run tests
console.log('Testing API endpoints...');
testStatisticsEndpoint();
testAttendanceRecordsEndpoint();
