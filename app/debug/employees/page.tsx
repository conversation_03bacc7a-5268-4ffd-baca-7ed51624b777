import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeDebug from '@/components/debug/EmployeeDebug';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Employee Debug | KaziSync',
  description: 'Debug employee fetching',
};

export default function EmployeeDebugPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <EmployeeDebug />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
