"use client";

import React, { useState } from 'react';
import Link from 'next/link';

const pricingPlans = [
  {
    name: 'Starter',
    monthlyPrice: 29,
    yearlyPrice: 290,
    description: 'Perfect for small businesses just getting started.',
    features: [
      'Up to 25 employees',
      'Basic attendance tracking',
      'Leave management',
      'Email support',
      'Mobile app access',
    ],
    cta: 'Get Started',
    highlighted: false,
  },
  {
    name: 'Professional',
    monthlyPrice: 79,
    yearlyPrice: 790,
    description: 'Ideal for growing businesses with advanced needs.',
    features: [
      'Up to 100 employees',
      'Advanced attendance tracking',
      'Shift management',
      'Reporting & analytics',
      'Priority email support',
      'API access',
      'Multiple locations',
    ],
    cta: 'Get Started',
    highlighted: true,
  },
  {
    name: 'Enterprise',
    monthlyPrice: 199,
    yearlyPrice: 1990,
    description: 'For large organizations requiring custom solutions.',
    features: [
      'Unlimited employees',
      'Custom integrations',
      'Advanced analytics',
      'Dedicated account manager',
      'Phone & email support',
      'Custom branding',
      'SLA guarantees',
    ],
    cta: 'Contact Sales',
    highlighted: false,
  },
];

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(true);

  return (
    <section id="pricing" className="section bg-white">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-secondary text-lg mb-8">
            Choose the plan that works best for your organization. All plans include a 14-day free trial.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${isAnnual ? 'text-secondary' : 'text-secondary-dark font-medium'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className="relative inline-flex h-6 w-12 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${isAnnual ? 'text-secondary-dark font-medium' : 'text-secondary'}`}>
              Annual <span className="text-success text-sm font-medium">(Save 20%)</span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <div
              key={index}
              className={`card overflow-hidden transition-all duration-300 ${
                plan.highlighted
                  ? 'border-2 border-primary relative transform hover:-translate-y-2'
                  : 'hover:border-gray-300'
              }`}
            >
              {plan.highlighted && (
                <div className="bg-primary text-white text-center py-1 text-sm font-medium">
                  Most Popular
                </div>
              )}
              <div className="p-6 md:p-8">
                <h3 className="text-2xl font-bold text-secondary-dark mb-2">{plan.name}</h3>
                <p className="text-secondary mb-6">{plan.description}</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-secondary-dark">
                    ${isAnnual ? plan.yearlyPrice : plan.monthlyPrice}
                  </span>
                  <span className="text-secondary">/{isAnnual ? 'year' : 'month'}</span>
                </div>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <svg
                        className="h-5 w-5 text-success mt-0.5 mr-2 flex-shrink-0"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-secondary-dark">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href={plan.name === 'Enterprise' ? '/contact' : '/signup'}
                  className={`w-full text-center py-3 rounded font-medium transition-colors ${
                    plan.highlighted
                      ? 'bg-primary text-white hover:bg-primary-dark'
                      : 'bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white'
                  }`}
                >
                  {plan.cta}
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-secondary-dark mb-4">
            Need a custom solution?
          </h3>
          <p className="text-secondary mb-6">
            Contact our sales team for a custom quote tailored to your organization's specific needs.
          </p>
          <Link href="/contact" className="btn-outline px-8 py-3">
            Contact Sales
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
