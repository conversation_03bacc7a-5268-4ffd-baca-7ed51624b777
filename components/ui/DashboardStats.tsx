"use client";

import React from 'react';

interface DashboardStatsProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  className?: string;
  loading?: boolean;
}

const DashboardStats: React.FC<DashboardStatsProps> = ({
  title,
  value,
  change,
  changeType,
  className = '',
  loading = false,
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600 text-sm">Loading...</span>
        </div>
      ) : (
        <>
          <p className="text-sm font-medium text-secondary mb-1">{title}</p>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-secondary-dark">{value}</p>
            <div
              className={`flex items-center text-xs font-medium ${
                changeType === 'positive'
                  ? 'text-green-600'
                  : changeType === 'negative'
                  ? 'text-red-600'
                  : 'text-gray-500'
              }`}
            >
              {changeType !== 'neutral' && (
                <svg
                  className={`h-3 w-3 mr-1 ${changeType === 'negative' ? 'transform rotate-180' : ''}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 10l7-7m0 0l7 7m-7-7v18"
                  />
                </svg>
              )}
              {change}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardStats;