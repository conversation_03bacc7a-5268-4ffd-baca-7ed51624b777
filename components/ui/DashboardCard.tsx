import React from 'react';

interface DashboardCardProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  loading?: boolean;
  action?: React.ReactNode;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ 
  children, 
  title, 
  className = '', 
  loading = false,
  action 
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      {title && (
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-secondary-dark">{title}</h3>
            {action && <div>{action}</div>}
          </div>
        </div>
      )}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading...</span>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default DashboardCard;