import React from 'react';

interface DashboardCardProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  loading?: boolean;
  action?: React.ReactNode;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ 
  children, 
  title, 
  className = '', 
  loading = false,
  action 
}) => {
  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden ${className}`}>
      {title && (
        <div className="px-6 py-5 border-b border-gray-100 bg-gray-50/50">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {action && <div>{action}</div>}
          </div>
        </div>
      )}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-200 border-t-blue-600"></div>
            <span className="ml-3 text-gray-600 font-medium">Loading...</span>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default DashboardCard;