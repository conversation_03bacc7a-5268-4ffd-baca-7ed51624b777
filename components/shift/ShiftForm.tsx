'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Shift, ShiftUpdateResponse, ShiftCreateResponse } from '@/types/shift';

interface ShiftFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  shift?: Shift | null;
  isEditing?: boolean;
}

const ShiftForm: React.FC<ShiftFormProps> = ({
  onSuccess,
  onCancel,
  shift = null,
  isEditing = false
}) => {
  const { companies } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_time: '09:00',
    end_time: '17:00',
    grace_period_late: 15,
    grace_period_early: 15,
    break_duration: 60,
    break_start_time: '13:00',
    is_night_shift: false,
    is_flexible: false,
    working_days: '1,2,3,4,5' // Monday to Friday by default
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Days of the week for the working days selector
  const daysOfWeek = [
    { id: 1, name: 'Monday', short: 'Mon' },
    { id: 2, name: 'Tuesday', short: 'Tue' },
    { id: 3, name: 'Wednesday', short: 'Wed' },
    { id: 4, name: 'Thursday', short: 'Thu' },
    { id: 5, name: 'Friday', short: 'Fri' },
    { id: 6, name: 'Saturday', short: 'Sat' },
    { id: 7, name: 'Sunday', short: 'Sun' }
  ];

  // Initialize form data if editing an existing shift
  useEffect(() => {
    if (isEditing && shift) {
      setFormData({
        name: shift.name || '',
        description: shift.description || '',
        start_time: shift.start_time || '09:00',
        end_time: shift.end_time || '17:00',
        grace_period_late: shift.grace_period_late || 15,
        grace_period_early: shift.grace_period_early || 15,
        break_duration: shift.break_duration || 60,
        break_start_time: shift.break_start_time || '13:00',
        is_night_shift: shift.is_night_shift || false,
        is_flexible: shift.is_flexible || false,
        working_days: shift.working_days || '1,2,3,4,5'
      });
    }
  }, [isEditing, shift]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleWorkingDaysChange = (dayId: number) => {
    // Toggle the day in the working_days string
    const currentDays = formData.working_days.split(',').map(Number);
    let newDays: number[];

    if (currentDays.includes(dayId)) {
      // Remove the day
      newDays = currentDays.filter(id => id !== dayId);
    } else {
      // Add the day
      newDays = [...currentDays, dayId].sort((a, b) => a - b);
    }

    // Update the form data
    setFormData({
      ...formData,
      working_days: newDays.join(',')
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.name.trim()) {
      setError('Shift name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.start_time) {
      setError('Start time is required');
      setIsLoading(false);
      return;
    }

    if (!formData.end_time) {
      setError('End time is required');
      setIsLoading(false);
      return;
    }

    if (!formData.working_days) {
      setError('At least one working day must be selected');
      setIsLoading(false);
      return;
    }

    try {
      // Get the company ID from the first company in the list
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        setError('No company found. Please register a company first.');
        setIsLoading(false);
        return;
      }

      // Prepare the data
      const shiftData = {
        company_id: companyId,
        name: formData.name.trim(),
        description: formData.description.trim(),
        start_time: formData.start_time,
        end_time: formData.end_time,
        grace_period_late: formData.grace_period_late,
        grace_period_early: formData.grace_period_early,
        break_duration: formData.break_duration,
        break_start_time: formData.break_start_time,
        is_night_shift: formData.is_night_shift,
        is_flexible: formData.is_flexible,
        working_days: formData.working_days
      };

      // Log the data being sent
      console.log('Submitting shift data:', shiftData);

      // Import API functions dynamically to avoid circular dependencies
      const { apiPost, apiPatch } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');

      // Get the access token
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let response;

      if (isEditing && shift) {
        // Update existing shift using PATCH method
        console.log('Updating shift with data:', shiftData);

        const updateResponse = await apiPatch<ShiftUpdateResponse>(`api/shifts/${shift.shift_id}?company_id=${companyId}`, shiftData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('Update shift response:', updateResponse);

        // Check if the response indicates success
        if ((updateResponse.code === 100 || updateResponse.success) &&
            (updateResponse.extend?.shift || updateResponse.shift)) {
          setSuccessMessage('Shift updated successfully!');
          response = updateResponse;
        } else {
          throw new Error(updateResponse.msg || 'Failed to update shift');
        }
      } else {
        // Create new shift
        const createResponse = await apiPost<ShiftCreateResponse>('api/shifts', shiftData, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Create shift response:', createResponse);

        // Check if the response indicates success
        if ((createResponse.code === 100 || createResponse.success) &&
            (createResponse.extend?.shift || createResponse.shift)) {
          setSuccessMessage('Shift created successfully!');
          response = createResponse;
        } else {
          throw new Error(createResponse.msg || 'Failed to create shift');
        }
      }

      console.log('Shift operation response:', response);

      // Reset the form
      if (!isEditing) {
        setFormData({
          name: '',
          description: '',
          start_time: '09:00',
          end_time: '17:00',
          grace_period_late: 15,
          grace_period_early: 15,
          break_duration: 60,
          break_start_time: '13:00',
          is_night_shift: false,
          is_flexible: false,
          working_days: '1,2,3,4,5'
        });
      }

      // Call the onSuccess callback if provided
      if (onSuccess) {
        // Wait a bit to show the success message before closing the modal
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (error: any) {
      console.error('Error with shift operation:', error);

      // Provide more detailed error message
      if (error.message && error.message.includes('401')) {
        setError('Authentication error: Your session may have expired. Please log in again.');
      } else if (error.message && error.message.includes('400')) {
        setError('Invalid data: Please check your input and try again.');
      } else {
        setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} shift. Please try again.`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold text-secondary-dark mb-4">
        {isEditing ? 'Edit Shift' : 'Create New Shift'}
      </h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-4">
          {successMessage}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-secondary-dark mb-1">
            Shift Name *
          </label>
          <input
            id="name"
            name="name"
            type="text"
            required
            value={formData.name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter shift name"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-secondary-dark mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter shift description"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="start_time" className="block text-sm font-medium text-secondary-dark mb-1">
              Start Time * <span className="text-xs text-secondary">(24-hour format)</span>
            </label>
            <input
              id="start_time"
              name="start_time"
              type="time"
              required
              value={formData.start_time}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="end_time" className="block text-sm font-medium text-secondary-dark mb-1">
              End Time * <span className="text-xs text-secondary">(24-hour format)</span>
            </label>
            <input
              id="end_time"
              name="end_time"
              type="time"
              required
              value={formData.end_time}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="break_start_time" className="block text-sm font-medium text-secondary-dark mb-1">
              Break Start Time <span className="text-xs text-secondary">(24-hour format)</span>
            </label>
            <input
              id="break_start_time"
              name="break_start_time"
              type="time"
              value={formData.break_start_time}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="break_duration" className="block text-sm font-medium text-secondary-dark mb-1">
              Break Duration (minutes)
            </label>
            <input
              id="break_duration"
              name="break_duration"
              type="number"
              min="0"
              value={formData.break_duration}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="grace_period_late" className="block text-sm font-medium text-secondary-dark mb-1">
              Grace Period Late (minutes)
            </label>
            <input
              id="grace_period_late"
              name="grace_period_late"
              type="number"
              min="0"
              value={formData.grace_period_late}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="grace_period_early" className="block text-sm font-medium text-secondary-dark mb-1">
              Grace Period Early (minutes)
            </label>
            <input
              id="grace_period_early"
              name="grace_period_early"
              type="number"
              min="0"
              value={formData.grace_period_early}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <input
              id="is_night_shift"
              name="is_night_shift"
              type="checkbox"
              checked={formData.is_night_shift}
              onChange={handleChange}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label htmlFor="is_night_shift" className="ml-2 block text-sm text-secondary-dark">
              Night Shift
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="is_flexible"
              name="is_flexible"
              type="checkbox"
              checked={formData.is_flexible}
              onChange={handleChange}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label htmlFor="is_flexible" className="ml-2 block text-sm text-secondary-dark">
              Flexible Hours
            </label>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-secondary-dark mb-2">
            Working Days *
          </label>
          <div className="flex flex-wrap gap-2">
            {daysOfWeek.map(day => {
              const isActive = formData.working_days.split(',').map(Number).includes(day.id);
              return (
                <button
                  key={day.id}
                  type="button"
                  onClick={() => handleWorkingDaysChange(day.id)}
                  className={`px-3 py-1 rounded-full text-sm ${
                    isActive
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-secondary-dark hover:bg-gray-200'
                  }`}
                >
                  {day.short}
                </button>
              );
            })}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline py-2 px-4"
              disabled={isLoading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary py-2 px-6 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              isEditing ? 'Update Shift' : 'Create Shift'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ShiftForm;
