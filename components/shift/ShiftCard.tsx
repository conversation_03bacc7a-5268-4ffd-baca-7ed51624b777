'use client';

import React, { useState } from 'react';
import { Shift, formatTime, getShortDayNames, calculateShiftDuration } from '@/types/shift';
import ShiftEmployeesModal from '@/components/shifts/ShiftEmployeesModal';
import ShiftAssignButton from '@/components/shifts/ShiftAssignButton';

interface ShiftCardProps {
  shift: Shift;
  onEdit: (shift: Shift) => void;
  onDelete: (shift: Shift) => void;
}

const ShiftCard: React.FC<ShiftCardProps> = ({ shift, onEdit, onDelete }) => {
  const [isEmployeesModalOpen, setIsEmployeesModalOpen] = useState(false);
  const [refreshEmployees, setRefreshEmployees] = useState(0);

  const openEmployeesModal = () => {
    setIsEmployeesModalOpen(true);
  };

  const closeEmployeesModal = () => {
    setIsEmployeesModalOpen(false);
  };

  const handleAssignmentSuccess = () => {
    // Increment the refresh counter to trigger a re-fetch in the employees modal
    setRefreshEmployees(prev => prev + 1);

    // If the employees modal is open, keep it open
    if (isEmployeesModalOpen) {
      // Force a refresh of the employees list
      setIsEmployeesModalOpen(false);
      setTimeout(() => {
        setIsEmployeesModalOpen(true);
      }, 300);
    }
  };
  // Get working days display
  const workingDays = getShortDayNames(shift.working_days || '');

  // Calculate shift duration
  const duration = calculateShiftDuration(
    shift.start_time,
    shift.end_time,
    shift.break_duration || 0
  );

  return (
    <>
      <ShiftEmployeesModal
        isOpen={isEmployeesModalOpen}
        onClose={closeEmployeesModal}
        shift={shift}
        refreshTrigger={refreshEmployees}
      />

      <div className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:border-gray-300 transition-colors duration-300">
        <div className={`h-2 ${shift.is_night_shift ? 'bg-indigo-600' : 'bg-primary'}`}></div>
        <div className="p-5">
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-lg font-semibold text-secondary-dark">{shift.name}</h3>
            <span className={`px-2 py-1 text-xs rounded-full ${
              shift.is_flexible
                ? 'bg-purple-100 text-purple-800'
                : shift.is_night_shift
                  ? 'bg-indigo-100 text-indigo-800'
                  : 'bg-green-100 text-green-800'
            }`}>
              {shift.is_flexible
                ? 'Flexible'
                : shift.is_night_shift
                  ? 'Night Shift'
                  : 'Regular'
              }
            </span>
          </div>

        {shift.description && (
          <p className="text-sm text-secondary mb-4 line-clamp-2">{shift.description}</p>
        )}

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-xs text-secondary">Start Time</p>
            <p className="text-sm font-medium text-secondary-dark">{formatTime(shift.start_time)}</p>
          </div>
          <div>
            <p className="text-xs text-secondary">End Time</p>
            <p className="text-sm font-medium text-secondary-dark">{formatTime(shift.end_time)}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-xs text-secondary">Duration</p>
            <p className="text-sm font-medium text-secondary-dark">{duration}</p>
          </div>
          <div>
            <p className="text-xs text-secondary">Break</p>
            <p className="text-sm font-medium text-secondary-dark">
              {shift.break_duration ? `${shift.break_duration} min` : 'None'}
              {shift.break_start_time ? ` at ${formatTime(shift.break_start_time)}` : ''}
            </p>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-xs text-secondary mb-1">Working Days</p>
          <div className="flex space-x-1">
            {workingDays.map((day, index) => (
              <span
                key={index}
                className={`w-7 h-7 flex items-center justify-center rounded-full text-xs ${
                  day.active
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 text-gray-400'
                }`}
              >
                {day.name.charAt(0)}
              </span>
            ))}
          </div>
        </div>

        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <div className="text-xs text-secondary">
            <span>Late grace: {shift.grace_period_late || 0} min</span>
            <span className="mx-1">•</span>
            <span>Early grace: {shift.grace_period_early || 0} min</span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={openEmployeesModal}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Employees
            </button>
            <ShiftAssignButton
              shift={shift}
              onAssignmentSuccess={handleAssignmentSuccess}
            />
            <button
              onClick={() => onEdit(shift)}
              className="text-primary hover:text-primary-dark text-sm"
            >
              Edit
            </button>
            <button
              onClick={() => onDelete(shift)}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default ShiftCard;
