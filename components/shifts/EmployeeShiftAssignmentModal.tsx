'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Shift as ShiftType } from '@/types/shift';

// Define interfaces
type Shift = ShiftType;

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

interface EmployeeShiftAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  shift?: Shift | null;
  employee?: Employee | null;
}

const EmployeeShiftAssignmentModal: React.FC<EmployeeShiftAssignmentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  shift,
  employee
}) => {
  const { companies } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [selectedShift, setSelectedShift] = useState<string>(shift?.shift_id || '');
  const [selectedEmployee, setSelectedEmployee] = useState<string>(employee?.employee_id || '');
  const [effectiveStartDate, setEffectiveStartDate] = useState<Date | null>(new Date());
  const [effectiveEndDate, setEffectiveEndDate] = useState<Date | null>(
    new Date(new Date().setMonth(new Date().getMonth() + 6))
  );
  const [customStartTime, setCustomStartTime] = useState<string>(shift?.start_time || '09:00');
  const [customEndTime, setCustomEndTime] = useState<string>(shift?.end_time || '17:00');
  const [customBreakDuration, setCustomBreakDuration] = useState<number>(shift?.break_duration || 60);
  const [customWorkingDays, setCustomWorkingDays] = useState<string>(shift?.working_days || '1,2,3,4,5');

  // Lists for dropdowns
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingShifts, setIsLoadingShifts] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);

  // Working days checkboxes
  const [workingDays, setWorkingDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false
  });

  // Initialize form with shift data if provided
  useEffect(() => {
    if (shift) {
      setSelectedShift(shift.shift_id);
      setCustomStartTime(shift.start_time);
      setCustomEndTime(shift.end_time);
      setCustomBreakDuration(shift.break_duration || 60);

      // Parse working days
      const days = (shift.working_days || '').split(',').map(d => parseInt(d));
      setWorkingDays({
        monday: days.includes(1),
        tuesday: days.includes(2),
        wednesday: days.includes(3),
        thursday: days.includes(4),
        friday: days.includes(5),
        saturday: days.includes(6),
        sunday: days.includes(0)
      });

      setCustomWorkingDays(shift.working_days || '1,2,3,4,5');
    }
  }, [shift]);

  // Initialize form with employee data if provided
  useEffect(() => {
    if (employee) {
      setSelectedEmployee(employee.employee_id);
    }
  }, [employee]);

  // Fetch shifts and employees when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchShifts();
      fetchEmployees();
    }
  }, [isOpen]);

  // Update working days string when checkboxes change
  useEffect(() => {
    const days = [];
    if (workingDays.monday) days.push(1);
    if (workingDays.tuesday) days.push(2);
    if (workingDays.wednesday) days.push(3);
    if (workingDays.thursday) days.push(4);
    if (workingDays.friday) days.push(5);
    if (workingDays.saturday) days.push(6);
    if (workingDays.sunday) days.push(0);

    setCustomWorkingDays(days.join(','));
  }, [workingDays]);

  // Fetch shifts
  const fetchShifts = async () => {
    try {
      setIsLoadingShifts(true);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {shifts: Shift[]}}>(
        `api/shifts?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.shifts) {
        setShifts(response.extend.shifts);

        // If no shift was provided but we have shifts, select the first one
        if (!shift && response.extend.shifts.length > 0) {
          const firstShift = response.extend.shifts[0];
          setSelectedShift(firstShift.shift_id);
          setCustomStartTime(firstShift.start_time);
          setCustomEndTime(firstShift.end_time);
          setCustomBreakDuration(firstShift.break_duration || 60);

          // Parse working days
          const days = (firstShift.working_days || '').split(',').map(d => parseInt(d));
          setWorkingDays({
            monday: days.includes(1),
            tuesday: days.includes(2),
            wednesday: days.includes(3),
            thursday: days.includes(4),
            friday: days.includes(5),
            saturday: days.includes(6),
            sunday: days.includes(0)
          });

          setCustomWorkingDays(firstShift.working_days || '1,2,3,4,5');
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch shifts');
    } finally {
      setIsLoadingShifts(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {employees: Employee[]}}>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        // Filter to only active employees
        const activeEmployees = response.extend.employees.filter(emp => emp.status === 'active');
        setEmployees(activeEmployees);

        // If no employee was provided but we have employees, select the first one
        if (!employee && activeEmployees.length > 0) {
          setSelectedEmployee(activeEmployees[0].employee_id);
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch employees');
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError('');
      setSuccess('');

      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }

      if (!selectedShift) {
        throw new Error('Please select a shift');
      }

      if (!selectedEmployee) {
        throw new Error('Please select an employee');
      }

      if (!effectiveStartDate) {
        throw new Error('Please select an effective start date');
      }

      if (!effectiveEndDate) {
        throw new Error('Please select an effective end date');
      }

      if (!customStartTime) {
        throw new Error('Please enter a custom start time');
      }

      if (!customEndTime) {
        throw new Error('Please enter a custom end time');
      }

      if (customWorkingDays === '') {
        throw new Error('Please select at least one working day');
      }

      const companyId = companies[0].company_id;
      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const formattedStartDate = effectiveStartDate.toISOString().split('T')[0];
      const formattedEndDate = effectiveEndDate.toISOString().split('T')[0];

      const assignmentData = {
        company_id: companyId,
        employee_id: selectedEmployee,
        shift_id: selectedShift,
        effective_start_date: formattedStartDate,
        effective_end_date: formattedEndDate,
        custom_start_time: customStartTime,
        custom_end_time: customEndTime,
        custom_break_duration: customBreakDuration,
        custom_working_days: customWorkingDays,
        is_active: true
      };

      await apiPost('api/employee-shifts', assignmentData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Employee successfully assigned to shift');

      // Close modal and refresh data after a short delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);

    } catch (error: any) {
      setError(error.message || 'Failed to assign employee to shift');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-60 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center sm:block sm:pt-20 sm:px-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Assign Employee to Shift
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mt-2 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
                    {success}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4">
                  <div className="space-y-4">
                    {/* Employee Selection */}
                    <div>
                      <label htmlFor="employee" className="block text-sm font-medium text-secondary-dark mb-1">
                        Employee
                      </label>
                      <select
                        id="employee"
                        value={selectedEmployee}
                        onChange={(e) => setSelectedEmployee(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        disabled={!!employee || isLoadingEmployees}
                      >
                        {isLoadingEmployees ? (
                          <option>Loading employees...</option>
                        ) : employees.length === 0 ? (
                          <option>No employees available</option>
                        ) : (
                          <>
                            <option value="">Select an employee</option>
                            {employees.map((emp) => (
                              <option key={emp.employee_id} value={emp.employee_id}>
                                {emp.full_name} ({emp.position || 'No position'})
                              </option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    {/* Shift Selection */}
                    <div>
                      <label htmlFor="shift" className="block text-sm font-medium text-secondary-dark mb-1">
                        Shift
                      </label>
                      <select
                        id="shift"
                        value={selectedShift}
                        onChange={(e) => {
                          setSelectedShift(e.target.value);
                          // Update custom fields based on selected shift
                          const selected = shifts.find(s => s.shift_id === e.target.value);
                          if (selected) {
                            setCustomStartTime(selected.start_time);
                            setCustomEndTime(selected.end_time);
                            setCustomBreakDuration(selected.break_duration || 60);

                            // Parse working days
                            const days = (selected.working_days || '').split(',').map(d => parseInt(d));
                            setWorkingDays({
                              monday: days.includes(1),
                              tuesday: days.includes(2),
                              wednesday: days.includes(3),
                              thursday: days.includes(4),
                              friday: days.includes(5),
                              saturday: days.includes(6),
                              sunday: days.includes(0)
                            });

                            setCustomWorkingDays(selected.working_days || '1,2,3,4,5');
                          }
                        }}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        disabled={!!shift || isLoadingShifts}
                      >
                        {isLoadingShifts ? (
                          <option>Loading shifts...</option>
                        ) : shifts.length === 0 ? (
                          <option>No shifts available</option>
                        ) : (
                          <>
                            <option value="">Select a shift</option>
                            {shifts.map((s) => (
                              <option key={s.shift_id} value={s.shift_id}>
                                {s.name} ({s.start_time} - {s.end_time})
                              </option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    {/* Effective Date Range */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-secondary-dark mb-1">
                          Start Date
                        </label>
                        <DatePicker
                          selected={effectiveStartDate}
                          onChange={(date) => setEffectiveStartDate(date)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          dateFormat="yyyy-MM-dd"
                          minDate={new Date()}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-secondary-dark mb-1">
                          End Date
                        </label>
                        <DatePicker
                          selected={effectiveEndDate}
                          onChange={(date) => setEffectiveEndDate(date)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          dateFormat="yyyy-MM-dd"
                          minDate={effectiveStartDate || undefined}
                        />
                      </div>
                    </div>

                    {/* Custom Times */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="customStartTime" className="block text-sm font-medium text-secondary-dark mb-1">
                          Custom Start Time
                        </label>
                        <input
                          type="time"
                          id="customStartTime"
                          value={customStartTime}
                          onChange={(e) => setCustomStartTime(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="customEndTime" className="block text-sm font-medium text-secondary-dark mb-1">
                          Custom End Time
                        </label>
                        <input
                          type="time"
                          id="customEndTime"
                          value={customEndTime}
                          onChange={(e) => setCustomEndTime(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    {/* Custom Break Duration */}
                    <div>
                      <label htmlFor="customBreakDuration" className="block text-sm font-medium text-secondary-dark mb-1">
                        Custom Break Duration (minutes)
                      </label>
                      <input
                        type="number"
                        id="customBreakDuration"
                        value={customBreakDuration}
                        onChange={(e) => setCustomBreakDuration(parseInt(e.target.value))}
                        min="0"
                        max="120"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>

                    {/* Working Days */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Working Days
                      </label>
                      <div className="grid grid-cols-7 gap-2">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="monday"
                            checked={workingDays.monday}
                            onChange={(e) => setWorkingDays({...workingDays, monday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="monday" className="ml-2 block text-sm text-secondary-dark">
                            Mon
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="tuesday"
                            checked={workingDays.tuesday}
                            onChange={(e) => setWorkingDays({...workingDays, tuesday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="tuesday" className="ml-2 block text-sm text-secondary-dark">
                            Tue
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="wednesday"
                            checked={workingDays.wednesday}
                            onChange={(e) => setWorkingDays({...workingDays, wednesday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="wednesday" className="ml-2 block text-sm text-secondary-dark">
                            Wed
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="thursday"
                            checked={workingDays.thursday}
                            onChange={(e) => setWorkingDays({...workingDays, thursday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="thursday" className="ml-2 block text-sm text-secondary-dark">
                            Thu
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="friday"
                            checked={workingDays.friday}
                            onChange={(e) => setWorkingDays({...workingDays, friday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="friday" className="ml-2 block text-sm text-secondary-dark">
                            Fri
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="saturday"
                            checked={workingDays.saturday}
                            onChange={(e) => setWorkingDays({...workingDays, saturday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="saturday" className="ml-2 block text-sm text-secondary-dark">
                            Sat
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="sunday"
                            checked={workingDays.sunday}
                            onChange={(e) => setWorkingDays({...workingDays, sunday: e.target.checked})}
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <label htmlFor="sunday" className="ml-2 block text-sm text-secondary-dark">
                            Sun
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? 'Assigning...' : 'Assign Employee'}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeShiftAssignmentModal;
