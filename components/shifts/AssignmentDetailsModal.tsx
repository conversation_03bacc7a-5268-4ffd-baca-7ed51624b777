'use client';

import React from 'react';
import { Shift } from '@/types/shift';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

interface ShiftAssignment {
  assignment_id: string;
  employee_id: string;
  shift_id: string;
  effective_start_date: string;
  effective_end_date: string;
  custom_start_time: string;
  custom_end_time: string;
  custom_break_duration: number;
  custom_working_days: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  employee?: Employee;
  shift?: Shift;
}

interface AssignmentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  assignment: ShiftAssignment;
}

const AssignmentDetailsModal: React.FC<AssignmentDetailsModalProps> = ({
  isOpen,
  onClose,
  assignment
}) => {
  if (!isOpen) return null;

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Format working days for display
  const formatWorkingDays = (daysString: string) => {
    const dayMap: {[key: string]: string} = {
      '0': 'Sunday',
      '1': 'Monday',
      '2': 'Tuesday',
      '3': 'Wednesday',
      '4': 'Thursday',
      '5': 'Friday',
      '6': 'Saturday'
    };

    return daysString.split(',').map(day => dayMap[day] || day).join(', ');
  };

  // Format time for display
  const formatTime = (time: string) => {
    if (!time) return '';

    // Parse the time string (HH:MM)
    const [hours, minutes] = time.split(':').map(Number);

    // Convert to 12-hour format
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

    // Format the time
    return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return (
    <div className="fixed inset-0 z-60 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Assignment Details
                </h3>

                <div className="mt-4 bg-gray-50 p-4 rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-secondary-dark">Employee</h4>
                      <p className="text-sm text-secondary mt-1">
                        {assignment.employee ? assignment.employee.full_name : 'Unknown Employee'}
                      </p>
                      {assignment.employee && (
                        <p className="text-xs text-secondary mt-1">
                          Position: {assignment.employee.position || 'No position'}
                        </p>
                      )}
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-secondary-dark">Shift</h4>
                      <p className="text-sm text-secondary mt-1">
                        {assignment.shift ? assignment.shift.name : 'Unknown Shift'}
                      </p>
                      {assignment.shift && (
                        <p className="text-xs text-secondary mt-1">
                          {assignment.shift.description || 'No description'}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <h4 className="text-sm font-medium text-secondary-dark">Assignment Period</h4>
                  <div className="mt-2 bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-secondary">Start Date</p>
                        <p className="text-sm text-secondary-dark">
                          {formatDate(assignment.effective_start_date)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">End Date</p>
                        <p className="text-sm text-secondary-dark">
                          {formatDate(assignment.effective_end_date)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <h4 className="text-sm font-medium text-secondary-dark">Custom Schedule</h4>
                  <div className="mt-2 bg-gray-50 p-4 rounded-md">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-secondary">Working Hours</p>
                        <p className="text-sm text-secondary-dark">
                          {formatTime(assignment.custom_start_time)} - {formatTime(assignment.custom_end_time)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Break Duration</p>
                        <p className="text-sm text-secondary-dark">
                          {assignment.custom_break_duration} minutes
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <p className="text-xs text-secondary">Working Days</p>
                      <p className="text-sm text-secondary-dark">
                        {formatWorkingDays(assignment.custom_working_days)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <h4 className="text-sm font-medium text-secondary-dark">Status</h4>
                  <div className="mt-2 bg-gray-50 p-4 rounded-md">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      assignment.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {assignment.is_active ? 'Active' : 'Inactive'}
                    </span>

                    <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-secondary">Created</p>
                        <p className="text-sm text-secondary-dark">
                          {formatDate(assignment.created_at)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Last Updated</p>
                        <p className="text-sm text-secondary-dark">
                          {formatDate(assignment.updated_at)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-lg border border-transparent px-6 py-3 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm transition-colors"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignmentDetailsModal;
