'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import EmployeeShiftAssignmentModal from './EmployeeShiftAssignmentModal';
import { Shift as ShiftType } from '@/types/shift';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

// Use the Shift type from types/shift.ts
type Shift = ShiftType;

interface ShiftAssignment {
  assignment_id: string;
  employee_id: string;
  shift_id: string;
  effective_start_date: string;
  effective_end_date: string;
  custom_start_time: string;
  custom_end_time: string;
  custom_break_duration: number;
  custom_working_days: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  employee?: Employee;
}

interface ShiftEmployeesModalProps {
  isOpen: boolean;
  onClose: () => void;
  shift: Shift;
  refreshTrigger?: number;
}

const ShiftEmployeesModal: React.FC<ShiftEmployeesModalProps> = ({
  isOpen,
  onClose,
  shift,
  refreshTrigger = 0
}) => {
  const { companies } = useAuth();
  const [assignments, setAssignments] = useState<ShiftAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Modal states for assigning a new employee
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  // Fetch employees assigned to this shift when modal opens or refreshTrigger changes
  useEffect(() => {
    if (isOpen && shift) {
      fetchAssignedEmployees();
    }
  }, [isOpen, shift, refreshTrigger]);

  // Function to fetch employees assigned to this shift
  const fetchAssignedEmployees = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0 || !shift) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {assignments: ShiftAssignment[]}}>(
        `api/employee-shifts?company_id=${companyId}&shift_id=${shift.shift_id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.assignments) {
        setAssignments(response.extend.assignments);
      } else {
        setAssignments([]);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch assigned employees');
    } finally {
      setIsLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Format working days for display
  const formatWorkingDays = (daysString: string) => {
    const dayMap: {[key: string]: string} = {
      '0': 'Sun',
      '1': 'Mon',
      '2': 'Tue',
      '3': 'Wed',
      '4': 'Thu',
      '5': 'Fri',
      '6': 'Sat'
    };

    return daysString.split(',').map(day => dayMap[day] || day).join(', ');
  };

  // Close assignment modal - kept for future use if needed
  const closeAssignModal = () => {
    setIsAssignModalOpen(false);
  };

  // Handle successful assignment
  const handleAssignmentSuccess = () => {
    fetchAssignedEmployees();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Employee Assignment Modal - Positioned outside the ShiftEmployeesModal */}
      <EmployeeShiftAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={closeAssignModal}
        onSuccess={handleAssignmentSuccess}
        shift={shift}
        employee={null}
      />

      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <div
            className="fixed inset-0 transition-opacity"
            aria-hidden="true"
            onClick={onClose}
          >
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          {/* Modal panel */}
          <div
            className="inline-block align-bottom bg-white rounded-2xl border border-gray-200 text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full"
            onClick={e => e.stopPropagation()}
          >
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Employees Assigned to {shift.name}
                    </h3>
                  </div>

                  <div className="mt-2">
                    <p className="text-sm text-secondary">
                      Shift Time: {shift.start_time} - {shift.end_time} | Working Days: {formatWorkingDays(shift.working_days || '')}
                    </p>
                  </div>

                  {error && (
                    <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                      {error}
                    </div>
                  )}

                  {isLoading ? (
                    <div className="py-8 text-center">
                      <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <p className="mt-2 text-secondary">Loading assigned employees...</p>
                    </div>
                  ) : assignments.length === 0 ? (
                    <div className="py-8 text-center">
                      <p className="text-secondary">No employees assigned to this shift yet.</p>
                      <p className="mt-2 text-xs text-secondary">
                        Use the "Assign" button on the shift card to assign employees to this shift.
                      </p>
                    </div>
                  ) : (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Employee
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Position
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Custom Times
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Working Days
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Effective Period
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                              Status
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {assignments.map((assignment) => (
                            <tr key={assignment.assignment_id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                                    <span className="text-sm font-medium">
                                      {assignment.employee?.first_name.charAt(0) || '?'}
                                      {assignment.employee?.last_name.charAt(0) || '?'}
                                    </span>
                                  </div>
                                  <div className="ml-4">
                                    <div className="text-sm font-medium text-secondary-dark">
                                      {assignment.employee?.full_name || 'Unknown Employee'}
                                    </div>
                                    <div className="text-xs text-secondary">
                                      {assignment.employee?.email || 'No email'}
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-secondary-dark">
                                  {assignment.employee?.position || 'No position'}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-secondary-dark">
                                  {assignment.custom_start_time} - {assignment.custom_end_time}
                                </div>
                                <div className="text-xs text-secondary">
                                  Break: {assignment.custom_break_duration} min
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-secondary-dark">
                                  {formatWorkingDays(assignment.custom_working_days)}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-secondary-dark">
                                  {formatDate(assignment.effective_start_date)} - {formatDate(assignment.effective_end_date)}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  assignment.is_active
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {assignment.is_active ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 px-6 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
                onClick={onClose}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ShiftEmployeesModal;
