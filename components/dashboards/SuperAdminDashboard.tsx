"use client";

import React from 'react';
import Link from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import DashboardChart from '@/components/ui/DashboardChart';

const SuperAdminDashboard = () => {
  // Mock data for demonstration
  const stats = [
    { title: 'Total Companies', value: '42', change: '+12%', changeType: 'positive' },
    { title: 'Active Subscriptions', value: '38', change: '+8%', changeType: 'positive' },
    { title: 'Total Revenue', value: '$24,500', change: '+15%', changeType: 'positive' },
    { title: 'Pending Issues', value: '7', change: '-3%', changeType: 'positive' },
  ];

  const recentCompanies = [
    { id: 1, name: 'Tech Innovations Inc.', plan: 'Enterprise', employees: 120, status: 'Active' },
    { id: 2, name: 'Global Solutions Ltd.', plan: 'Professional', employees: 45, status: 'Active' },
    { id: 3, name: 'Retail Chain Group', plan: 'Professional', employees: 78, status: 'Active' },
    { id: 4, name: 'Healthcare Services', plan: 'Starter', employees: 23, status: 'Trial' },
    { id: 5, name: 'Education Systems', plan: 'Professional', employees: 56, status: 'Active' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Super Admin Dashboard</h1>
        <div>
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Company
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType as 'positive' | 'negative' | 'neutral'}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Subscription Revenue Chart */}
        <div className="lg:col-span-2">
          <DashboardCard title="Subscription Revenue">
            <div className="h-80">
              <DashboardChart
                type="line"
                labels={['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']}
                datasets={[
                  {
                    label: 'Revenue',
                    data: [3000, 3500, 4200, 4800, 5100, 5500],
                    borderColor: '#007BFF',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>

        {/* Subscription Distribution */}
        <div>
          <DashboardCard title="Subscription Plans">
            <div className="h-80">
              <DashboardChart
                type="doughnut"
                labels={['Starter', 'Professional', 'Enterprise']}
                datasets={[
                  {
                    data: [12, 19, 8],
                    backgroundColor: ['#28A745', '#007BFF', '#6C757D'],
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>
      </div>

      {/* Recent Companies */}
      <DashboardCard title="Recent Companies">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Employees
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentCompanies.map((company) => (
                <tr key={company.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-secondary-dark">{company.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{company.plan}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{company.employees}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        company.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : company.status === 'Trial'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {company.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href={`/dashboard/super-admin/companies/${company.id}`} className="text-primary hover:text-primary-dark mr-3">
                      View
                    </Link>
                    <Link href={`/dashboard/super-admin/companies/${company.id}/edit`} className="text-secondary hover:text-secondary-dark">
                      Edit
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </DashboardCard>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DashboardCard title="System Status">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-dark">API Status</span>
              <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-dark">Database Status</span>
              <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-dark">Storage Status</span>
              <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-dark">Email Service</span>
              <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Operational
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-secondary-dark">SMS Service</span>
              <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                Degraded
              </span>
            </div>
          </div>
        </DashboardCard>

        <DashboardCard title="Recent Audit Logs">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {[1, 2, 3, 4, 5, 6, 7].map((_, index) => (
              <div key={index} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-secondary-dark">
                    {index % 3 === 0
                      ? 'User Login'
                      : index % 3 === 1
                      ? 'Company Created'
                      : 'Subscription Updated'}
                  </span>
                  <span className="text-xs text-secondary-light">
                    {index === 0 ? '2 minutes ago' : index === 1 ? '1 hour ago' : `${index} days ago`}
                  </span>
                </div>
                <p className="text-xs text-secondary mt-1">
                  {index % 3 === 0
                    ? 'Admin user logged in from ***********'
                    : index % 3 === 1
                    ? 'New company "Tech Solutions" was created'
                    : 'Subscription for "Global Corp" was upgraded to Enterprise'}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/super-admin/audit-logs" className="text-sm text-primary hover:text-primary-dark">
              View all logs
            </Link>
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
