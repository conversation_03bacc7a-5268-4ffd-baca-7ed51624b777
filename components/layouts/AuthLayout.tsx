import React from 'react';
import Link from 'next/link';

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm py-4">
        <div className="container-custom">
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-primary">KaziSync</span>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md">
          <div className="bg-white shadow-md rounded-lg p-8">
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white py-4 border-t">
        <div className="container-custom">
          <div className="text-center text-sm text-secondary">
            <p>&copy; {new Date().getFullYear()} KaziSync. All rights reserved.</p>
            <div className="mt-2 space-x-4">
              <Link href="/terms" className="text-secondary hover:text-primary">
                Terms of Service
              </Link>
              <Link href="/privacy" className="text-secondary hover:text-primary">
                Privacy Policy
              </Link>
              <Link href="/contact" className="text-secondary hover:text-primary">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AuthLayout;
