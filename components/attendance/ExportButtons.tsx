'use client';

import React, { useRef } from 'react';
import { saveAs } from 'file-saver';
import { utils, write } from 'xlsx';
import html2canvas from 'html2canvas';

interface ExportButtonsProps {
  data: any;
}

const ExportButtons: React.FC<ExportButtonsProps> = ({ data }) => {
  const dashboardRef = useRef<HTMLDivElement | null>(null);

  // Export to PNG
  const exportToPNG = async () => {
    if (!dashboardRef.current) {
      dashboardRef.current = document.querySelector('.space-y-6') as HTMLDivElement;
    }

    if (dashboardRef.current) {
      try {
        const canvas = await html2canvas(dashboardRef.current);

        canvas.toBlob((blob) => {
          if (blob) {
            saveAs(blob, `attendance_dashboard_${new Date().toISOString().split('T')[0]}.png`);
          }
        });
      } catch (error) {
        console.error('Error exporting to PNG:', error);
      }
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    if (!data) return;

    try {
      // Determine which statistics to use based on period
      let periodData: any[] = [];

      if (data.weekly_statistics) {
        periodData = data.weekly_statistics;
      } else if (data.monthly_statistics) {
        periodData = data.monthly_statistics;
      } else if (data.annual_statistics) {
        periodData = data.annual_statistics;
      } else if (data.custom_statistics) {
        periodData = data.custom_statistics;
      }

      // Create workbook and worksheet
      const wb = utils.book_new();

      // Add period data
      if (periodData.length > 0) {
        const ws = utils.json_to_sheet(periodData);
        utils.book_append_sheet(wb, ws, 'Attendance Data');
      }

      // Add department data
      if (data.department_statistics && data.department_statistics.length > 0) {
        const deptWs = utils.json_to_sheet(data.department_statistics);
        utils.book_append_sheet(wb, deptWs, 'Department Statistics');
      }

      // Add summary data
      if (data.summary) {
        const summaryWs = utils.json_to_sheet([data.summary]);
        utils.book_append_sheet(wb, summaryWs, 'Summary');
      }

      // Generate file and trigger download
      const wbout = write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });
      saveAs(blob, `attendance_data_${new Date().toISOString().split('T')[0]}.xlsx`);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
    }
  };

  return (
    <div className="flex gap-2">
      <button
        onClick={exportToPNG}
        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        disabled={!data}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        Export PNG
      </button>
      <button
        onClick={exportToCSV}
        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        disabled={!data}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Export CSV
      </button>
    </div>
  );
};

export default ExportButtons;
