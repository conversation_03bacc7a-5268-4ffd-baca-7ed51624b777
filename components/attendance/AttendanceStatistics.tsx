'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { Bar, Line } from 'react-chartjs-2';
import DepartmentStatisticsTable from './DepartmentStatisticsTable';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Types for statistics data
interface DayStatistics {
  date: string;
  day_of_week: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface DepartmentStatistics {
  department_id: string;
  department_name: string;
  employee_count: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  avg_attendance_percentage: number;
}

interface StatisticsSummary {
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface StatisticsMetadata {
  period: string;
  period_description: string;
  start_date: string;
  end_date: string;
  total_days: number;
  total_employees: number;
}

interface AttendanceStatisticsData {
  status: string;
  metadata: StatisticsMetadata;
  summary: StatisticsSummary;
  department_statistics: DepartmentStatistics[];
  weekly_statistics?: DayStatistics[];
  monthly_statistics?: DayStatistics[];
  annual_statistics?: {
    month: string;
    present_count: number;
    absent_count: number;
    late_count: number;
    on_leave_count: number;
    attendance_percentage: number;
  }[];
  custom_statistics?: DayStatistics[];
}

const AttendanceStatistics: React.FC = () => {
  const { companies } = useAuth();
  const [statisticsData, setStatisticsData] = useState<AttendanceStatisticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Period selection state
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'annual' | 'custom'>('weekly');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  // Define fetchStatistics function with useCallback
  const fetchStatistics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let endpoint = `api/attendance/statistics?company_id=${companyId}&period=${selectedPeriod}`;

      // Add date parameters for custom period
      if (selectedPeriod === 'custom' && startDate && endDate) {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        endpoint += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
      }

      console.log(`Fetching statistics from: ${endpoint}`);

      const response = await apiGet<AttendanceStatisticsData>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      setStatisticsData(response);
    } catch (error: any) {
      console.error('Error fetching statistics:', error);
      setError(error.message || 'Failed to fetch attendance statistics');
    } finally {
      setIsLoading(false);
    }
  }, [companies, selectedPeriod, startDate, endDate]);

  // Set default dates when period changes
  useEffect(() => {
    // Only update dates if they're not already set for the current period
    // This prevents infinite loops
    const setDatesForPeriod = () => {
      if (selectedPeriod === 'weekly') {
        const today = new Date();
        const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const mondayOfWeek = new Date(today);
        mondayOfWeek.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));

        const sundayOfWeek = new Date(mondayOfWeek);
        sundayOfWeek.setDate(mondayOfWeek.getDate() + 6);

        setStartDate(mondayOfWeek);
        setEndDate(sundayOfWeek);
      } else if (selectedPeriod === 'monthly') {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        setStartDate(firstDayOfMonth);
        setEndDate(lastDayOfMonth);
      } else if (selectedPeriod === 'annual') {
        const today = new Date();
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
        const lastDayOfYear = new Date(today.getFullYear(), 11, 31);

        setStartDate(firstDayOfYear);
        setEndDate(lastDayOfYear);
      }
    };

    setDatesForPeriod();
  }, [selectedPeriod]);

  // Fetch statistics when dependencies change
  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  // Prepare chart data based on period
  const getChartData = () => {
    if (!statisticsData) return null;

    let labels: string[] = [];
    let presentData: number[] = [];
    let absentData: number[] = [];
    let lateData: number[] = [];
    let onLeaveData: number[] = [];

    if (selectedPeriod === 'weekly' && statisticsData.weekly_statistics) {
      labels = statisticsData.weekly_statistics.map(day => day.day_of_week);
      presentData = statisticsData.weekly_statistics.map(day => day.present_count);
      absentData = statisticsData.weekly_statistics.map(day => day.absent_count);
      lateData = statisticsData.weekly_statistics.map(day => day.late_count);
      onLeaveData = statisticsData.weekly_statistics.map(day => day.on_leave_count);
    } else if (selectedPeriod === 'monthly' && statisticsData.monthly_statistics) {
      labels = statisticsData.monthly_statistics.map(day => day.date.split('-')[2]); // Day of month
      presentData = statisticsData.monthly_statistics.map(day => day.present_count);
      absentData = statisticsData.monthly_statistics.map(day => day.absent_count);
      lateData = statisticsData.monthly_statistics.map(day => day.late_count);
      onLeaveData = statisticsData.monthly_statistics.map(day => day.on_leave_count);
    } else if (selectedPeriod === 'annual' && statisticsData.annual_statistics) {
      labels = statisticsData.annual_statistics.map(month => month.month);
      presentData = statisticsData.annual_statistics.map(month => month.present_count);
      absentData = statisticsData.annual_statistics.map(month => month.absent_count);
      lateData = statisticsData.annual_statistics.map(month => month.late_count);
      onLeaveData = statisticsData.annual_statistics.map(month => month.on_leave_count);
    } else if (selectedPeriod === 'custom' && statisticsData.custom_statistics) {
      labels = statisticsData.custom_statistics.map(day => day.date);
      presentData = statisticsData.custom_statistics.map(day => day.present_count);
      absentData = statisticsData.custom_statistics.map(day => day.absent_count);
      lateData = statisticsData.custom_statistics.map(day => day.late_count);
      onLeaveData = statisticsData.custom_statistics.map(day => day.on_leave_count);
    }

    return {
      labels,
      datasets: [
        {
          label: 'Present',
          data: presentData,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
        {
          label: 'Absent',
          data: absentData,
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        },
        {
          label: 'Late',
          data: lateData,
          backgroundColor: 'rgba(255, 206, 86, 0.6)',
          borderColor: 'rgba(255, 206, 86, 1)',
          borderWidth: 1,
        },
        {
          label: 'On Leave',
          data: onLeaveData,
          backgroundColor: 'rgba(153, 102, 255, 0.6)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  // Chart options
  const barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: statisticsData?.metadata.period_description || 'Attendance Statistics',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Line chart options
  const lineChartOptions: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: statisticsData?.metadata.period_description || 'Attendance Statistics',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Handle period change
  const handlePeriodChange = (period: 'weekly' | 'monthly' | 'annual' | 'custom') => {
    setSelectedPeriod(period);
  };

  return (
    <div className="space-y-6">
      <DashboardCard title="Attendance Statistics">
        <div className="mb-6">
          <div className="flex flex-wrap gap-4 mb-4">
            <button
              onClick={() => handlePeriodChange('weekly')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedPeriod === 'weekly'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Weekly
            </button>
            <button
              onClick={() => handlePeriodChange('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedPeriod === 'monthly'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => handlePeriodChange('annual')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedPeriod === 'annual'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Annual
            </button>
            <button
              onClick={() => handlePeriodChange('custom')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedPeriod === 'custom'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Custom
            </button>
          </div>

          {selectedPeriod === 'custom' && (
            <div className="flex flex-wrap gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  Start Date
                </label>
                <DatePicker
                  selected={startDate}
                  onChange={(date) => setStartDate(date)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  dateFormat="yyyy-MM-dd"
                  maxDate={endDate || new Date()}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-dark mb-1">
                  End Date
                </label>
                <DatePicker
                  selected={endDate}
                  onChange={(date) => setEndDate(date)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  dateFormat="yyyy-MM-dd"
                  minDate={startDate || undefined}
                  maxDate={new Date()}
                />
              </div>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading attendance statistics...</p>
          </div>
        ) : statisticsData ? (
          <div className="space-y-6">
            {/* Period Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-secondary-dark mb-2">
                {statisticsData.metadata.period_description}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-sm text-secondary">Total Employees</p>
                  <p className="text-xl font-bold text-secondary-dark">{statisticsData.metadata.total_employees}</p>
                </div>
                <div className="bg-white p-3 rounded-md shadow-sm">
                  <p className="text-sm text-secondary">Present</p>
                  <p className="text-xl font-bold text-green-600">{statisticsData.summary.present_count}</p>
                </div>
                <div className="bg-white p-3 rounded-md shadow-sm">
                  <p className="text-sm text-secondary">Absent</p>
                  <p className="text-xl font-bold text-red-600">{statisticsData.summary.absent_count}</p>
                </div>
                <div className="bg-white p-3 rounded-md shadow-sm">
                  <p className="text-sm text-secondary">Late</p>
                  <p className="text-xl font-bold text-yellow-600">{statisticsData.summary.late_count}</p>
                </div>
                <div className="bg-white p-3 rounded-md shadow-sm">
                  <p className="text-sm text-secondary">On Leave</p>
                  <p className="text-xl font-bold text-purple-600">{statisticsData.summary.on_leave_count}</p>
                </div>
              </div>
              <div className="mt-4 bg-white p-3 rounded-md shadow-sm">
                <p className="text-sm text-secondary">Attendance Percentage</p>
                <div className="relative pt-1">
                  <div className="flex mb-2 items-center justify-between">
                    <div>
                      <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-primary bg-primary-50">
                        {statisticsData.summary.attendance_percentage.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-primary-100">
                    <div
                      style={{ width: `${statisticsData.summary.attendance_percentage}%` }}
                      className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Attendance Chart */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="text-lg font-medium text-secondary-dark mb-4">Attendance Trend</h3>
              <div className="h-80">
                {getChartData() && (
                  selectedPeriod === 'annual' ? (
                    <Line data={getChartData()!} options={lineChartOptions} />
                  ) : (
                    <Bar data={getChartData()!} options={barChartOptions} />
                  )
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="py-8 text-center">
            <p className="text-secondary">No attendance statistics available for the selected period.</p>
          </div>
        )}
      </DashboardCard>

      {/* Department Statistics Table */}
      {statisticsData && (
        <DepartmentStatisticsTable
          departmentStatistics={statisticsData.department_statistics}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default AttendanceStatistics;
