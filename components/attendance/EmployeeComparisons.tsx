'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface EmployeeComparisonsProps {
  data: {
    employee: {
      full_name: string;
    };
    statistics: {
      attendance: {
        attendance_rate: number;
        punctuality_rate: number;
      };
      time: {
        efficiency_rate: number;
      };
    };
    comparisons: {
      company: {
        avg_attendance_rate: number;
        avg_efficiency_rate: number;
        avg_punctuality_rate: number;
        employee_rank: number;
        total_employees: number;
      };
      department: {
        avg_attendance_rate: number;
        avg_efficiency_rate: number;
        avg_punctuality_rate: number;
        employee_count: number;
        employee_rank: number;
        name: string;
      };
    };
  };
}

const EmployeeComparisons: React.FC<EmployeeComparisonsProps> = ({ data }) => {
  const { employee, statistics, comparisons } = data;

  // Check if required data exists
  if (!employee || !statistics || !comparisons) {
    return (
      <DashboardCard title="Performance Comparisons">
        <div className="py-8 text-center">
          <p className="text-gray-500">No comparison data available</p>
        </div>
      </DashboardCard>
    );
  }

  // Helper function to get comparison color
  const getComparisonColor = (employeeValue: number, avgValue: number) => {
    if (employeeValue > avgValue) {
      return 'text-green-600 bg-green-50';
    } else if (employeeValue < avgValue) {
      return 'text-red-600 bg-red-50';
    } else {
      return 'text-yellow-600 bg-yellow-50';
    }
  };

  // Helper function to get comparison icon
  const getComparisonIcon = (employeeValue: number, avgValue: number) => {
    if (employeeValue > avgValue) {
      return '↑';
    } else if (employeeValue < avgValue) {
      return '↓';
    } else {
      return '=';
    }
  };

  // Helper function to format percentage
  const formatPercentage = (value: number) => {
    return (value * 100).toFixed(1);
  };

  // Helper function to get rank suffix
  const getRankSuffix = (rank: number) => {
    if (rank % 10 === 1 && rank % 100 !== 11) return 'st';
    if (rank % 10 === 2 && rank % 100 !== 12) return 'nd';
    if (rank % 10 === 3 && rank % 100 !== 13) return 'rd';
    return 'th';
  };

  return (
    <DashboardCard title="Performance Comparisons">
      <div className="space-y-6">
        {/* Company Comparison */}
        <div>
          <h4 className="text-lg font-medium text-secondary-dark mb-4">
            Company-wide Comparison
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Attendance Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Attendance Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.attendance.attendance_rate, comparisons.company.avg_attendance_rate)
                }`}>
                  {getComparisonIcon(statistics.attendance.attendance_rate, comparisons.company.avg_attendance_rate)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{formatPercentage(statistics.attendance.attendance_rate)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Company Avg:</span>
                  <span className="text-sm">{formatPercentage(comparisons.company.avg_attendance_rate)}%</span>
                </div>
              </div>
            </div>

            {/* Punctuality Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Punctuality Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.attendance.punctuality_rate / 100, comparisons.company.avg_punctuality_rate / 100)
                }`}>
                  {getComparisonIcon(statistics.attendance.punctuality_rate / 100, comparisons.company.avg_punctuality_rate / 100)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{statistics.attendance.punctuality_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Company Avg:</span>
                  <span className="text-sm">{comparisons.company.avg_punctuality_rate.toFixed(1)}%</span>
                </div>
              </div>
            </div>

            {/* Efficiency Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Efficiency Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.time.efficiency_rate, comparisons.company.avg_efficiency_rate)
                }`}>
                  {getComparisonIcon(statistics.time.efficiency_rate, comparisons.company.avg_efficiency_rate)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{formatPercentage(statistics.time.efficiency_rate)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Company Avg:</span>
                  <span className="text-sm">{formatPercentage(comparisons.company.avg_efficiency_rate)}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Company Ranking */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">Company Ranking</span>
              <span className="text-lg font-semibold text-blue-800">
                {comparisons.company.employee_rank}{getRankSuffix(comparisons.company.employee_rank)} of {comparisons.company.total_employees}
              </span>
            </div>
          </div>
        </div>

        {/* Department Comparison */}
        <div>
          <h4 className="text-lg font-medium text-secondary-dark mb-4">
            Department Comparison ({comparisons.department.name})
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Attendance Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Attendance Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.attendance.attendance_rate, comparisons.department.avg_attendance_rate)
                }`}>
                  {getComparisonIcon(statistics.attendance.attendance_rate, comparisons.department.avg_attendance_rate)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{formatPercentage(statistics.attendance.attendance_rate)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Dept Avg:</span>
                  <span className="text-sm">{formatPercentage(comparisons.department.avg_attendance_rate)}%</span>
                </div>
              </div>
            </div>

            {/* Punctuality Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Punctuality Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.attendance.punctuality_rate / 100, comparisons.department.avg_punctuality_rate / 100)
                }`}>
                  {getComparisonIcon(statistics.attendance.punctuality_rate / 100, comparisons.department.avg_punctuality_rate / 100)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{statistics.attendance.punctuality_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Dept Avg:</span>
                  <span className="text-sm">{comparisons.department.avg_punctuality_rate.toFixed(1)}%</span>
                </div>
              </div>
            </div>

            {/* Efficiency Rate */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Efficiency Rate</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  getComparisonColor(statistics.time.efficiency_rate, comparisons.department.avg_efficiency_rate)
                }`}>
                  {getComparisonIcon(statistics.time.efficiency_rate, comparisons.department.avg_efficiency_rate)}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">You:</span>
                  <span className="text-sm font-medium">{formatPercentage(statistics.time.efficiency_rate)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Dept Avg:</span>
                  <span className="text-sm">{formatPercentage(comparisons.department.avg_efficiency_rate)}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Department Ranking */}
          <div className="mt-4 p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-green-700">Department Ranking</span>
              <span className="text-lg font-semibold text-green-800">
                {comparisons.department.employee_rank}{getRankSuffix(comparisons.department.employee_rank)} of {comparisons.department.employee_count}
              </span>
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default EmployeeComparisons;
