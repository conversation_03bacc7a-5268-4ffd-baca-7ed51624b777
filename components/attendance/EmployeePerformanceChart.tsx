'use client';

import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import DashboardCard from '@/components/ui/DashboardCard';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PeriodComparison {
  attendance_rate: number;
  end_date: string;
  period: string;
  present_days: number;
  start_date: string;
  working_days: number;
}

interface EmployeePerformanceChartProps {
  data: {
    trends: {
      period_comparison: PeriodComparison[];
      trend_direction: string;
      trend_strength: number;
    };
    statistics: {
      attendance: {
        attendance_rate: number;
      };
    };
  };
}

const EmployeePerformanceChart: React.FC<EmployeePerformanceChartProps> = ({ data }) => {
  const { trends, statistics } = data;

  // Check if trends and period_comparison exist
  if (!trends || !trends.period_comparison || !Array.isArray(trends.period_comparison)) {
    return (
      <DashboardCard title="Performance Trend">
        <div className="h-64 flex items-center justify-center">
          <p className="text-gray-500">No trend data available</p>
        </div>
      </DashboardCard>
    );
  }

  // Prepare chart data
  const chartData = {
    labels: trends.period_comparison.map(period => {
      // Format period labels based on the period string
      if (period.period.includes('2025')) {
        // For monthly data, extract month name
        return period.period.split(' ')[0];
      }
      return period.period;
    }),
    datasets: [
      {
        label: 'Attendance Rate (%)',
        data: trends.period_comparison.map(period => period.attendance_rate),
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      }
    ]
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const index = context.dataIndex;
            const period = trends.period_comparison[index];

            return [
              `Attendance Rate: ${period.attendance_rate.toFixed(1)}%`,
              `Present Days: ${period.present_days}`,
              `Working Days: ${period.working_days}`
            ];
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Attendance Rate (%)'
        },
        ticks: {
          callback: (value: any) => `${value}%`
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    }
  };

  // Get trend direction color and icon
  const getTrendInfo = () => {
    switch (trends.trend_direction.toLowerCase()) {
      case 'improving':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          icon: '↗',
          text: 'Improving'
        };
      case 'declining':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          icon: '↘',
          text: 'Declining'
        };
      case 'stable':
        return {
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          icon: '→',
          text: 'Stable'
        };
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          icon: '—',
          text: 'Unknown'
        };
    }
  };

  const trendInfo = getTrendInfo();

  return (
    <DashboardCard title="Performance Trend">
      <div className="space-y-4">
        {/* Trend Summary */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`px-3 py-1 rounded-full ${trendInfo.bgColor}`}>
              <span className={`text-sm font-medium ${trendInfo.color}`}>
                {trendInfo.icon} {trendInfo.text}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              Strength: {(trends.trend_strength * 100).toFixed(1)}%
            </div>
          </div>

          <div className="text-right">
            <div className="text-lg font-semibold text-secondary-dark">
              {(statistics.attendance.attendance_rate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-500">Current Rate</div>
          </div>
        </div>

        {/* Chart */}
        <div className="h-64">
          {trends.period_comparison.length > 0 ? (
            <Line data={chartData} options={options} />
          ) : (
            <div className="h-full flex items-center justify-center">
              <p className="text-gray-500">No trend data available</p>
            </div>
          )}
        </div>

        {/* Period Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-secondary-dark">
              {trends.period_comparison.reduce((sum, period) => sum + period.present_days, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Present Days</div>
          </div>

          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-secondary-dark">
              {trends.period_comparison.reduce((sum, period) => sum + period.working_days, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Working Days</div>
          </div>

          <div className="text-center p-3 bg-gray-50 rounded-lg col-span-2 md:col-span-1">
            <div className="text-lg font-semibold text-secondary-dark">
              {trends.period_comparison.length > 0
                ? (trends.period_comparison.reduce((sum, period) => sum + period.attendance_rate, 0) / trends.period_comparison.length).toFixed(1)
                : 0}%
            </div>
            <div className="text-sm text-gray-600">Average Rate</div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default EmployeePerformanceChart;
