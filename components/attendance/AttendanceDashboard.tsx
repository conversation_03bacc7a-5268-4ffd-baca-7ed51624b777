'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import PeriodSelector from './PeriodSelector';
import SummaryCards from './SummaryCards';
import Trend<PERSON>hart from './TrendChart';
import DepartmentComparisonChart from './DepartmentComparisonChart';
import DetailedTable from './DetailedTable';
import ExportButtons from './ExportButtons';
import DashboardCard from '@/components/ui/DashboardCard';

// Types for statistics data
interface DayStatistics {
  date: string;
  day_of_week: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface DepartmentStatistics {
  department_id: string;
  department_name: string;
  employee_count: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  avg_attendance_percentage: number;
}

interface StatisticsSummary {
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface StatisticsMetadata {
  period: string;
  period_description: string;
  start_date: string;
  end_date: string;
  total_days: number;
  total_employees: number;
}

interface AttendanceStatisticsData {
  status: string;
  metadata: StatisticsMetadata;
  summary: StatisticsSummary;
  department_statistics: DepartmentStatistics[];
  weekly_statistics?: DayStatistics[];
  monthly_statistics?: DayStatistics[];
  annual_statistics?: {
    month: string;
    present_count: number;
    absent_count: number;
    late_count: number;
    on_leave_count: number;
    attendance_percentage: number;
  }[];
  custom_statistics?: DayStatistics[];
}

const AttendanceDashboard: React.FC = () => {
  const { companies } = useAuth();
  const [statisticsData, setStatisticsData] = useState<AttendanceStatisticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'annual' | 'custom'>('weekly');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');

  // Define fetchStatistics function with useCallback
  const fetchStatistics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError('');
      
      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }
      
      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();
      
      if (!token) {
        throw new Error('Authentication required');
      }
      
      let endpoint = `api/attendance/statistics?company_id=${companyId}&period=${selectedPeriod}`;
      
      // Add date parameters for custom period
      if (selectedPeriod === 'custom' && startDate && endDate) {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        endpoint += `&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
      }
      
      console.log(`Fetching statistics from: ${endpoint}`);
      
      const response = await apiGet<AttendanceStatisticsData>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      
      setStatisticsData(response);
    } catch (error: any) {
      console.error('Error fetching statistics:', error);
      setError(error.message || 'Failed to fetch attendance statistics');
    } finally {
      setIsLoading(false);
    }
  }, [companies, selectedPeriod, startDate, endDate]);
  
  // Set default dates when period changes
  useEffect(() => {
    // Only update dates if they're not already set for the current period
    // This prevents infinite loops
    const setDatesForPeriod = () => {
      if (selectedPeriod === 'weekly') {
        const today = new Date();
        const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const mondayOfWeek = new Date(today);
        mondayOfWeek.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
        
        const sundayOfWeek = new Date(mondayOfWeek);
        sundayOfWeek.setDate(mondayOfWeek.getDate() + 6);
        
        setStartDate(mondayOfWeek);
        setEndDate(sundayOfWeek);
      } else if (selectedPeriod === 'monthly') {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        setStartDate(firstDayOfMonth);
        setEndDate(lastDayOfMonth);
      } else if (selectedPeriod === 'annual') {
        const today = new Date();
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
        const lastDayOfYear = new Date(today.getFullYear(), 11, 31);
        
        setStartDate(firstDayOfYear);
        setEndDate(lastDayOfYear);
      }
    };
    
    setDatesForPeriod();
  }, [selectedPeriod]);
  
  // Fetch statistics when dependencies change
  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  // Get period-specific statistics
  const getPeriodStatistics = () => {
    if (!statisticsData) return [];
    
    if (selectedPeriod === 'weekly' && statisticsData.weekly_statistics) {
      return statisticsData.weekly_statistics;
    } else if (selectedPeriod === 'monthly' && statisticsData.monthly_statistics) {
      return statisticsData.monthly_statistics;
    } else if (selectedPeriod === 'annual' && statisticsData.annual_statistics) {
      return statisticsData.annual_statistics;
    } else if (selectedPeriod === 'custom' && statisticsData.custom_statistics) {
      return statisticsData.custom_statistics;
    }
    
    return [];
  };

  // Handle period change
  const handlePeriodChange = (period: 'weekly' | 'monthly' | 'annual' | 'custom') => {
    setSelectedPeriod(period);
  };

  // Handle department change
  const handleDepartmentChange = (departmentId: string) => {
    setSelectedDepartment(departmentId);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold text-secondary-dark">
          {statisticsData?.metadata.period_description || 'Attendance Dashboard'}
        </h1>
        <div className="flex flex-wrap gap-2">
          <ExportButtons data={statisticsData} />
        </div>
      </div>

      <PeriodSelector 
        selectedPeriod={selectedPeriod} 
        onPeriodChange={handlePeriodChange}
        startDate={startDate}
        endDate={endDate}
        onStartDateChange={setStartDate}
        onEndDateChange={setEndDate}
      />

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading attendance statistics...</p>
        </div>
      ) : statisticsData ? (
        <>
          <SummaryCards metadata={statisticsData.metadata} summary={statisticsData.summary} />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DashboardCard title="Attendance Trend">
              <TrendChart 
                data={getPeriodStatistics()} 
                period={selectedPeriod} 
              />
            </DashboardCard>
            
            <DashboardCard title="Department Comparison">
              <DepartmentComparisonChart 
                departments={statisticsData.department_statistics} 
              />
            </DashboardCard>
          </div>
          
          <DashboardCard title="Detailed Attendance">
            <div className="mb-4">
              <label htmlFor="department-filter" className="block text-sm font-medium text-secondary-dark mb-1">
                Filter by Department
              </label>
              <select
                id="department-filter"
                value={selectedDepartment}
                onChange={(e) => handleDepartmentChange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm w-full md:w-64"
              >
                <option value="all">All Departments</option>
                {statisticsData.department_statistics.map((dept) => (
                  <option key={dept.department_id} value={dept.department_id}>
                    {dept.department_name}
                  </option>
                ))}
              </select>
            </div>
            
            <DetailedTable 
              data={getPeriodStatistics()} 
              period={selectedPeriod}
              departmentId={selectedDepartment}
            />
          </DashboardCard>
        </>
      ) : (
        <div className="py-8 text-center">
          <p className="text-secondary">No attendance statistics available.</p>
        </div>
      )}
    </div>
  );
};

export default AttendanceDashboard;
