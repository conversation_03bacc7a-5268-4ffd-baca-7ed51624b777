'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import EmployeeSelector from './EmployeeSelector';
import PeriodSelector from './PeriodSelector';
import EmployeeStatsOverview from './EmployeeStatsOverview';
import EmployeePerformanceChart from './EmployeePerformanceChart';
import EmployeeComparisons from './EmployeeComparisons';
import EmployeeAIInsights from './EmployeeAIInsights';
import EmployeeTrendAnalysis from './EmployeeTrendAnalysis';

// Types for employee statistics data
interface EmployeeFromAPI {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone_number: string;
  position: string;
  status: string;
  hire_date: string;
  id_number: string;
  tenure_days: number;
  department: {
    department_id: string;
    name: string;
  };
}

interface AttendanceStats {
  absent_days: number;
  attendance_rate: number;
  consistency_score: number;
  late_days: number;
  on_leave_days: number;
  present_days: number;
  punctuality_rate: number;
}

interface TimeStats {
  average_daily_hours: number;
  efficiency_rate: number;
  expected_hours: number;
  overtime_hours: number;
  productivity_score: number;
  total_hours: number;
  undertime_hours: number;
}

interface ShiftComplianceStats {
  compliance_rate: number;
  compliant_days: number;
  early_arrivals: number;
  early_departures: number;
  has_shifts: boolean;
  late_arrivals: number;
  late_departures: number;
  shift_violations: string[];
  total_shift_days: number;
}

interface PeriodComparison {
  attendance_rate: number;
  end_date: string;
  period: string;
  present_days: number;
  start_date: string;
  working_days: number;
}

interface AIInsights {
  behavioral_patterns: {
    anomalies: string[];
    consistency_level: string;
    punctuality_trend: string;
    work_hours_pattern: string;
  };
  performance_summary: {
    areas_for_improvement: string[];
    overall_score: number;
    risk_level: string;
    strengths: string[];
  };
  predictive_indicators: {
    attendance_risk: string;
    burnout_risk: string;
    performance_trend: string;
  };
  recommendations: {
    immediate_actions: string[];
    long_term_goals: string[];
    manager_actions: string[];
  };
}

interface EmployeeAttendanceData {
  status: string;
  employee: Employee;
  period: {
    end_date: string;
    period_description: string;
    reference_date: string;
    start_date: string;
    total_days: number;
    type: string;
    working_days: number;
  };
  statistics: {
    attendance: AttendanceStats;
    time: TimeStats;
    shift_compliance: ShiftComplianceStats;
  };
  trends: {
    improvement_areas: string[];
    period_comparison: PeriodComparison[];
    recommendations: string[];
    trend_direction: string;
    trend_strength: number;
  };
  comparisons: {
    company: {
      avg_attendance_rate: number;
      avg_efficiency_rate: number;
      avg_punctuality_rate: number;
      employee_rank: number;
      total_employees: number;
    };
    department: {
      avg_attendance_rate: number;
      avg_efficiency_rate: number;
      avg_punctuality_rate: number;
      employee_count: number;
      employee_rank: number;
      name: string;
    };
  };
  ai_insights: AIInsights;
  time_patterns: {
    average_check_in_time: string | null;
    average_check_out_time: string | null;
    day_of_week_patterns: Record<string, any>;
    most_common_check_in_hour: number | null;
    most_common_check_out_hour: number | null;
    time_consistency: number;
  };
}

interface EmployeeAttendanceStatisticsProps {
  initialEmployeeId?: string;
}

const EmployeeAttendanceStatistics: React.FC<EmployeeAttendanceStatisticsProps> = ({
  initialEmployeeId
}) => {
  const { companies } = useAuth();
  const [employeeData, setEmployeeData] = useState<EmployeeAttendanceData | null>(null);
  const [employees, setEmployees] = useState<EmployeeFromAPI[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>(initialEmployeeId || '');
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'annual'>('weekly');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [error, setError] = useState('');

  // Fetch employees list
  const fetchEmployees = useCallback(async () => {
    try {
      if (!companies || companies.length === 0) {
        console.log('No companies available');
        return;
      }

      setIsLoadingEmployees(true);
      setError('');

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        console.log('No access token available');
        setError('Authentication required');
        return;
      }

      console.log(`Fetching employees for company: ${companyId}`);

      const endpoint = `api/employees?company_id=${companyId}`;
      console.log(`Full API endpoint: ${endpoint}`);
      console.log(`Authorization token present: ${!!token}`);
      console.log(`Token preview: ${token ? token.substring(0, 20) + '...' : 'No token'}`);

      const response = await apiGet<any>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      console.log('Employees API response:', response);
      console.log('Response type:', typeof response);
      console.log('Response keys:', response ? Object.keys(response) : 'No response');

      if (response && response.extend && response.extend.employees && Array.isArray(response.extend.employees)) {
        const employeesList = response.extend.employees;
        console.log('Found employees:', employeesList.length);

        setEmployees(employeesList);
        if (!selectedEmployee && employeesList.length > 0) {
          setSelectedEmployee(employeesList[0].employee_id);
        }
      } else {
        console.log('Response structure check:');
        console.log('- response exists:', !!response);
        console.log('- response.extend exists:', !!(response && response.extend));
        console.log('- response.extend.employees exists:', !!(response && response.extend && response.extend.employees));
        console.log('- is array:', !!(response && response.extend && response.extend.employees && Array.isArray(response.extend.employees)));

        // Try alternative response structures
        if (response && response.employees && Array.isArray(response.employees)) {
          console.log('Found employees in direct structure');
          setEmployees(response.employees);
          if (!selectedEmployee && response.employees.length > 0) {
            setSelectedEmployee(response.employees[0].employee_id);
          }
        } else if (response && Array.isArray(response)) {
          console.log('Response is direct array');
          setEmployees(response);
          if (!selectedEmployee && response.length > 0) {
            setSelectedEmployee(response[0].employee_id);
          }
        } else {
          console.log('No employees found in any expected structure');
          setError('No employees found for this company');
        }
      }
    } catch (error: any) {
      console.error('Error fetching employees:', error);
      setError(`Failed to fetch employees: ${error.message}`);
    } finally {
      setIsLoadingEmployees(false);
    }
  }, [companies, selectedEmployee]);

  // Fetch employee attendance statistics
  const fetchEmployeeStats = useCallback(async () => {
    try {
      if (!selectedEmployee || !companies || companies.length === 0) return;

      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const endpoint = `api/attendance/employee/${selectedEmployee}/statistics?company_id=${companyId}&period=${selectedPeriod}`;

      console.log(`Fetching employee statistics from: ${endpoint}`);

      const response = await apiGet<EmployeeAttendanceData>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      setEmployeeData(response);
    } catch (error: any) {
      console.error('Error fetching employee statistics:', error);
      setError(error.message || 'Failed to fetch employee statistics');
    } finally {
      setIsLoading(false);
    }
  }, [companies, selectedEmployee, selectedPeriod]);

  // Load employees on component mount
  useEffect(() => {
    fetchEmployees();
  }, [fetchEmployees]);

  // Fetch stats when employee or period changes
  useEffect(() => {
    if (selectedEmployee) {
      fetchEmployeeStats();
    }
  }, [fetchEmployeeStats]);

  // Handle employee change
  const handleEmployeeChange = (employeeId: string) => {
    setSelectedEmployee(employeeId);
  };

  // Handle period change
  const handlePeriodChange = (period: 'weekly' | 'monthly' | 'annual') => {
    setSelectedPeriod(period);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold text-secondary-dark">
          Employee Attendance Statistics
        </h1>
      </div>

      {/* Employee and Period Selection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <EmployeeSelector
          employees={employees}
          selectedEmployee={selectedEmployee}
          onEmployeeChange={handleEmployeeChange}
          isLoading={isLoadingEmployees}
        />
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <label className="block text-sm font-medium text-secondary-dark mb-2">
            Period
          </label>
          <div className="flex gap-2">
            <button
              onClick={() => handlePeriodChange('weekly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedPeriod === 'weekly'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Weekly
            </button>
            <button
              onClick={() => handlePeriodChange('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedPeriod === 'monthly'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => handlePeriodChange('annual')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedPeriod === 'annual'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Annual
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading employee statistics...</p>
        </div>
      ) : employeeData ? (
        <>
          <EmployeeStatsOverview data={employeeData} />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EmployeePerformanceChart data={employeeData} />
            <EmployeeComparisons data={employeeData} />
          </div>

          {employeeData.ai_insights && (
            <EmployeeAIInsights insights={employeeData.ai_insights} />
          )}

          <EmployeeTrendAnalysis trends={employeeData.trends} period={selectedPeriod} />
        </>
      ) : (
        <div className="py-8 text-center">
          <p className="text-secondary">
            {selectedEmployee ? 'No statistics available for the selected employee.' : 'Please select an employee to view statistics.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default EmployeeAttendanceStatistics;
