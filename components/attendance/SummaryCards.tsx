'use client';

import React from 'react';

interface StatisticsMetadata {
  period: string;
  period_description: string;
  start_date: string;
  end_date: string;
  total_days: number;
  total_employees: number;
}

interface StatisticsSummary {
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface SummaryCardsProps {
  metadata: StatisticsMetadata;
  summary: StatisticsSummary;
}

const SummaryCards: React.FC<SummaryCardsProps> = ({ metadata, summary }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Employees Card */}
      <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 font-medium">Total Employees</p>
            <h3 className="text-2xl font-bold text-secondary-dark mt-1">{metadata.total_employees}</h3>
          </div>
          <div className="p-3 bg-blue-50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-2">For period: {metadata.period_description}</p>
      </div>

      {/* Present Card */}
      <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 font-medium">Present</p>
            <h3 className="text-2xl font-bold text-green-600 mt-1">{summary.present_count}</h3>
          </div>
          <div className="p-3 bg-green-50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          {((summary.present_count / (metadata.total_employees * metadata.total_days)) * 100).toFixed(1)}% of total possible attendance
        </p>
      </div>

      {/* Absent Card */}
      <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 font-medium">Absent</p>
            <h3 className="text-2xl font-bold text-red-600 mt-1">{summary.absent_count}</h3>
          </div>
          <div className="p-3 bg-red-50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          {((summary.absent_count / (metadata.total_employees * metadata.total_days)) * 100).toFixed(1)}% of total possible attendance
        </p>
      </div>

      {/* Attendance Percentage Card */}
      <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 font-medium">Attendance %</p>
            <h3 className="text-2xl font-bold text-primary mt-1">{summary.attendance_percentage.toFixed(1)}%</h3>
          </div>
          <div className="relative h-16 w-16">
            {/* Circular progress indicator */}
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <circle
                cx="18"
                cy="18"
                r="16"
                fill="none"
                className="stroke-current text-gray-200"
                strokeWidth="2"
              />
              <circle
                cx="18"
                cy="18"
                r="16"
                fill="none"
                className="stroke-current text-primary"
                strokeWidth="2"
                strokeDasharray="100"
                strokeDashoffset={100 - summary.attendance_percentage}
                strokeLinecap="round"
                transform="rotate(-90 18 18)"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-primary">
              {summary.attendance_percentage.toFixed(1)}%
            </div>
          </div>
        </div>
        <p className="text-xs text-gray-500 mt-2">Overall attendance rate for the period</p>
      </div>
    </div>
  );
};

export default SummaryCards;
