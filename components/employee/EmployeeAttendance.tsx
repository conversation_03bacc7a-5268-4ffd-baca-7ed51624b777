'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import DashboardChart from '@/components/ui/DashboardChart';



interface AttendanceStatistics {
  employee: {
    employee_id: string;
    first_name: string;
    last_name: string;
    full_name: string;
    email: string;
    phone_number: string;
    position: string;
    department: {
      department_id: string;
      name: string;
    };
    hire_date: string;
    tenure_days: number;
  };
  period: {
    start_date: string;
    end_date: string;
    period_description: string;
    type: string;
    total_days: number;
    working_days: number;
  };
  statistics: {
    attendance: {
      present_days: number;
      absent_days: number;
      late_days: number;
      on_leave_days: number;
      attendance_rate: number;
      punctuality_rate: number;
      consistency_score: number;
    };
    time: {
      total_hours: number;
      expected_hours: number;
      overtime_hours: number;
      undertime_hours: number;
      average_daily_hours: number;
      efficiency_rate: number;
      productivity_score: number;
    };
    shift_compliance: {
      compliance_rate: number;
      compliant_days: number;
      total_shift_days: number;
      early_arrivals: number;
      late_arrivals: number;
      early_departures: number;
      late_departures: number;
    };
  };
  trends: {
    period_comparison: Array<{
      period: string;
      start_date: string;
      end_date: string;
      present_days: number;
      working_days: number;
      attendance_rate: number;
    }>;
    trend_direction: string;
    trend_strength: number;
  };
  ai_insights: {
    performance_summary: {
      overall_score: number;
      risk_level: string;
      strengths: string[];
      areas_for_improvement: string[];
    };
    behavioral_patterns: {
      punctuality_trend: string;
      consistency_level: string;
      work_hours_pattern: string;
      anomalies: string[];
    };
    recommendations: {
      immediate_actions: string[];
      long_term_goals: string[];
      manager_actions: string[];
    };
  };
  comparisons: {
    department: {
      name: string;
      employee_count: number;
      employee_rank: number;
      avg_attendance_rate: number;
      avg_punctuality_rate: number;
    };
    company: {
      total_employees: number;
      employee_rank: number;
      avg_attendance_rate: number;
      avg_punctuality_rate: number;
    };
  };
}

interface ExtendedUser {
  employee_id: string;
  employee_info: {
    created_at: string;
    department_id: string;
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string | null;
    last_name: string;
    phone_number: string | null;
    position: string | null;
    status: string;
    updated_at: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

const EmployeeAttendance = () => {
  const { user, companies } = useAuth();
  const extendedUser = user as ExtendedUser;
  const [statistics, setStatistics] = useState<AttendanceStatistics | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'annual'>('monthly');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);



  const fetchAttendanceStatistics = async () => {
    try {
      if (!companies || companies.length === 0) {
        setStatsLoading(false);
        return;
      }

      // Get employee ID from user object
      let employeeId = null;
      if (extendedUser?.employee_id) {
        employeeId = extendedUser.employee_id;
      } else if (extendedUser?.employee_info?.employee_id) {
        employeeId = extendedUser.employee_info.employee_id;
      }

      if (!employeeId) {
        setStatsLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setStatsLoading(true);

      // Fetch attendance statistics
      const statisticsUrl = `api/attendance/employee/${employeeId}/statistics?company_id=${companyId}&period=${selectedPeriod}&date=${selectedDate}`;
      console.log('Fetching statistics from:', statisticsUrl);

      const response = await apiGet<AttendanceStatistics>(
        statisticsUrl,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      console.log('Statistics response:', response);
      setStatistics(response);
      setStatsLoading(false);
    } catch (error) {
      console.error('Error fetching attendance statistics:', error);

      // Set mock statistics data for development/testing
      const mockStatistics: AttendanceStatistics = {
        employee: {
          employee_id: extendedUser?.employee_id || extendedUser?.employee_info?.employee_id || 'mock-employee',
          first_name: 'John',
          last_name: 'Doe',
          full_name: 'John Doe',
          email: '<EMAIL>',
          phone_number: '+1234567890',
          position: 'Software Developer',
          department: {
            department_id: 'dept-1',
            name: 'Engineering'
          },
          hire_date: '2023-01-15',
          tenure_days: 500
        },
        period: {
          start_date: '2025-06-01',
          end_date: '2025-06-07',
          period_description: 'Week of June 1-7, 2025',
          type: selectedPeriod,
          total_days: 7,
          working_days: 5
        },
        statistics: {
          attendance: {
            present_days: 4,
            absent_days: 1,
            late_days: 1,
            on_leave_days: 0,
            attendance_rate: 80,
            punctuality_rate: 75,
            consistency_score: 85
          },
          time: {
            total_hours: 32,
            expected_hours: 40,
            overtime_hours: 0,
            undertime_hours: 8,
            average_daily_hours: 6.4,
            efficiency_rate: 80,
            productivity_score: 75
          },
          shift_compliance: {
            compliance_rate: 90,
            compliant_days: 4,
            total_shift_days: 5,
            early_arrivals: 0,
            late_arrivals: 1,
            early_departures: 0,
            late_departures: 0
          }
        },
        trends: {
          period_comparison: [
            { period: 'Week 1', start_date: '2025-05-01', end_date: '2025-05-07', present_days: 5, working_days: 5, attendance_rate: 100 },
            { period: 'Week 2', start_date: '2025-05-08', end_date: '2025-05-14', present_days: 4, working_days: 5, attendance_rate: 80 },
            { period: 'Week 3', start_date: '2025-05-15', end_date: '2025-05-21', present_days: 5, working_days: 5, attendance_rate: 100 },
            { period: 'Week 4', start_date: '2025-05-22', end_date: '2025-05-28', present_days: 3, working_days: 5, attendance_rate: 60 }
          ],
          trend_direction: 'stable',
          trend_strength: 0.2
        },
        ai_insights: {
          performance_summary: {
            overall_score: 75,
            risk_level: 'medium',
            strengths: ['Good punctuality', 'Consistent work hours'],
            areas_for_improvement: ['Reduce absences', 'Improve attendance consistency']
          },
          behavioral_patterns: {
            punctuality_trend: 'stable',
            consistency_level: 'medium',
            work_hours_pattern: 'regular',
            anomalies: ['Occasional late arrivals']
          },
          recommendations: {
            immediate_actions: ['Set up morning reminders', 'Review commute options'],
            long_term_goals: ['Achieve 95% attendance rate'],
            manager_actions: ['Schedule check-in meeting']
          }
        },
        comparisons: {
          department: {
            name: 'Engineering',
            employee_count: 10,
            employee_rank: 6,
            avg_attendance_rate: 85,
            avg_punctuality_rate: 80
          },
          company: {
            total_employees: 50,
            employee_rank: 25,
            avg_attendance_rate: 82,
            avg_punctuality_rate: 78
          }
        }
      };

      setStatistics(mockStatistics);
      console.log('Set mock statistics data');
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    fetchAttendanceStatistics();
  }, [companies, extendedUser?.employee_id, extendedUser?.employee_info?.employee_id, selectedPeriod, selectedDate]);

  const handlePeriodChange = (period: 'weekly' | 'monthly' | 'annual') => {
    setSelectedPeriod(period);
  };

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
  };

  // Generate stats data from statistics
  const statsData = statistics ? [
    {
      title: 'Attendance Rate',
      value: `${statistics.statistics.attendance.attendance_rate.toFixed(1)}%`,
      change: '',
      changeType: statistics.statistics.attendance.attendance_rate >= 90 ? 'positive' as const : statistics.statistics.attendance.attendance_rate >= 70 ? 'neutral' as const : 'negative' as const
    },
    {
      title: 'Present Days',
      value: statistics.statistics.attendance.present_days.toString(),
      change: '',
      changeType: 'positive' as const
    },
    {
      title: 'Absent Days',
      value: statistics.statistics.attendance.absent_days.toString(),
      change: '',
      changeType: 'negative' as const
    },
    {
      title: 'Punctuality Rate',
      value: `${statistics.statistics.attendance.punctuality_rate.toFixed(1)}%`,
      change: '',
      changeType: statistics.statistics.attendance.punctuality_rate >= 90 ? 'positive' as const : 'neutral' as const
    },
    {
      title: 'Total Hours',
      value: statistics.statistics.time.total_hours.toFixed(1),
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Productivity Score',
      value: `${statistics.statistics.time.productivity_score}/100`,
      change: '',
      changeType: statistics.statistics.time.productivity_score >= 80 ? 'positive' as const : statistics.statistics.time.productivity_score >= 60 ? 'neutral' as const : 'negative' as const
    },
  ] : [];

  // Generate chart data for trends
  const trendChartData = statistics?.trends.period_comparison ? {
    labels: statistics.trends.period_comparison.map(item => item.period),
    datasets: [
      {
        label: 'Attendance Rate (%)',
        data: statistics.trends.period_comparison.map(item => item.attendance_rate),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Present Days',
        data: statistics.trends.period_comparison.map(item => item.present_days),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      }
    ]
  } : null;

  return (
    <div className="space-y-6">
      {/* Debug Information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-yellow-800">Debug Info</h3>
          <div className="text-xs text-yellow-700 mt-2 space-y-1">
            <p>Companies: {companies ? companies.length : 'null'}</p>
            <p>Company ID: {companies && companies.length > 0 ? companies[0].company_id : 'none'}</p>
            <p>Employee ID: {extendedUser?.employee_id || extendedUser?.employee_info?.employee_id || 'not found'}</p>
            <p>Stats Loading: {statsLoading.toString()}</p>
            <p>Statistics: {statistics ? 'loaded' : 'null'}</p>
            <p>Selected Period: {selectedPeriod}</p>
            <p>Selected Date: {selectedDate}</p>
          </div>
          <div className="mt-3 space-x-2">
            <button
              onClick={fetchAttendanceStatistics}
              className="px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
            >
              Retry Statistics
            </button>
          </div>
        </div>
      )}

      {/* Header with Period Selection */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Attendance</h1>
          {statistics && (
            <p className="text-sm text-gray-600 mt-1">
              {statistics.period.period_description} • {statistics.employee.department.name} Department
            </p>
          )}
          {!statistics && !statsLoading && (
            <p className="text-sm text-gray-600 mt-1">
              No attendance data available
            </p>
          )}
        </div>

        {/* Period and Date Controls */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex bg-gray-100 rounded-lg p-1">
            {(['weekly', 'monthly', 'annual'] as const).map((period) => (
              <button
                key={period}
                onClick={() => handlePeriodChange(period)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  selectedPeriod === period
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => handleDateChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Employee Info Card */}
      {statistics && (
        <DashboardCard title="Employee Information">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Employee ID</p>
              <p className="text-sm text-gray-900">{statistics.employee.employee_id}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Position</p>
              <p className="text-sm text-gray-900">{statistics.employee.position}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Hire Date</p>
              <p className="text-sm text-gray-900">{statistics.employee.hire_date}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Tenure</p>
              <p className="text-sm text-gray-900">{statistics.employee.tenure_days} days</p>
            </div>
          </div>
        </DashboardCard>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {statsData.length > 0 ? (
          statsData.map((stat, index) => (
            <DashboardStats
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              loading={statsLoading}
            />
          ))
        ) : (
          !statsLoading && (
            <div className="col-span-full">
              <DashboardCard>
                <div className="text-center py-8">
                  <p className="text-gray-500">No attendance statistics available</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Please check your employee ID and company settings
                  </p>
                </div>
              </DashboardCard>
            </div>
          )
        )}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Trend Chart */}
        {trendChartData && (
          <DashboardCard title="Attendance Trends" loading={statsLoading}>
            <div className="h-80">
              <DashboardChart
                type="line"
                labels={trendChartData.labels}
                datasets={trendChartData.datasets}
                height="320px"
              />
            </div>
          </DashboardCard>
        )}

        {/* Performance Insights */}
        {statistics?.ai_insights && (
          <DashboardCard title="AI Performance Insights">
            <div className="space-y-4">
              {/* Overall Score */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900">Overall Performance Score</p>
                  <p className="text-xs text-gray-500">Based on attendance, punctuality, and productivity</p>
                </div>
                <div className="text-right">
                  <p className={`text-2xl font-bold ${
                    statistics.ai_insights.performance_summary.overall_score >= 80 ? 'text-green-600' :
                    statistics.ai_insights.performance_summary.overall_score >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {statistics.ai_insights.performance_summary.overall_score}/100
                  </p>
                  <p className={`text-xs font-medium ${
                    statistics.ai_insights.performance_summary.risk_level === 'low' ? 'text-green-600' :
                    statistics.ai_insights.performance_summary.risk_level === 'medium' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {statistics.ai_insights.performance_summary.risk_level.toUpperCase()} RISK
                  </p>
                </div>
              </div>

              {/* Strengths */}
              {statistics.ai_insights.performance_summary.strengths.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-green-800 mb-2">✓ Strengths</h4>
                  <ul className="space-y-1">
                    {statistics.ai_insights.performance_summary.strengths.map((strength, index) => (
                      <li key={index} className="text-xs text-green-700 bg-green-50 px-2 py-1 rounded">
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Areas for Improvement */}
              {statistics.ai_insights.performance_summary.areas_for_improvement.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-orange-800 mb-2">⚠ Areas for Improvement</h4>
                  <ul className="space-y-1">
                    {statistics.ai_insights.performance_summary.areas_for_improvement.map((area, index) => (
                      <li key={index} className="text-xs text-orange-700 bg-orange-50 px-2 py-1 rounded">
                        {area}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Recommendations */}
              {statistics.ai_insights.recommendations.immediate_actions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-blue-800 mb-2">💡 Recommendations</h4>
                  <ul className="space-y-1">
                    {statistics.ai_insights.recommendations.immediate_actions.slice(0, 3).map((action, index) => (
                      <li key={index} className="text-xs text-blue-700 bg-blue-50 px-2 py-1 rounded">
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </DashboardCard>
        )}
      </div>

      {/* Department and Company Comparison */}
      {statistics?.comparisons && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DashboardCard title="Department Ranking">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-900">Your Rank in {statistics.comparisons.department.name}</span>
                <span className="text-lg font-bold text-blue-600">
                  #{statistics.comparisons.department.employee_rank} of {statistics.comparisons.department.employee_count}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Your Attendance Rate</span>
                  <span className="font-medium">{statistics.statistics.attendance.attendance_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Department Average</span>
                  <span className="font-medium">{statistics.comparisons.department.avg_attendance_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Your Punctuality Rate</span>
                  <span className="font-medium">{statistics.statistics.attendance.punctuality_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Department Average</span>
                  <span className="font-medium">{statistics.comparisons.department.avg_punctuality_rate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </DashboardCard>

          <DashboardCard title="Company Ranking">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-900">Your Company Rank</span>
                <span className="text-lg font-bold text-purple-600">
                  #{statistics.comparisons.company.employee_rank} of {statistics.comparisons.company.total_employees}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Your Attendance Rate</span>
                  <span className="font-medium">{statistics.statistics.attendance.attendance_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Company Average</span>
                  <span className="font-medium">{statistics.comparisons.company.avg_attendance_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Your Punctuality Rate</span>
                  <span className="font-medium">{statistics.statistics.attendance.punctuality_rate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Company Average</span>
                  <span className="font-medium">{statistics.comparisons.company.avg_punctuality_rate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </DashboardCard>
        </div>
      )}


    </div>
  );
};

export default EmployeeAttendance;
