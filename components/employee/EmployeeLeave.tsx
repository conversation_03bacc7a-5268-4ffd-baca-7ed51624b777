'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';

interface LeaveRequest {
  request_id: string;
  employee_id: string;
  employee_name: string;
  leave_type_id: string;
  leave_type_name: string;
  start_date: string;
  end_date: string;
  total_days: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  documentation_path?: string;
  created_at: string;
  updated_at: string;
}

interface LeaveRequestsResponse {
  extend: {
    leave_requests: LeaveRequest[];
    pagination: {
      has_next: boolean;
      has_prev: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  msg: string;
}

interface LeaveBalance {
  leave_type: string;
  total_days: number;
  used_days: number;
  remaining_days: number;
}

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}

interface ExtendedUser {
  employee_id: string;
  employee_info: {
    employee_id: string;
    full_name: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

const EmployeeLeave = () => {
  const { user, companies } = useAuth();
  const extendedUser = user as ExtendedUser;
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      if (!companies || companies.length === 0) {
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.error('Error fetching leave types:', error);
    }
  };

  const fetchLeaveData = async () => {
    try {
      if (!companies || companies.length === 0 || !extendedUser?.employee_info?.employee_id) {
        setLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const employeeId = extendedUser.employee_info.employee_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading(true);

      // Fetch leave types
      await fetchLeaveTypes();

      // Mock data as fallback
      const mockLeaveRequests: LeaveRequest[] = [
        {
          request_id: '1',
          employee_id: employeeId,
          employee_name: extendedUser?.employee_info?.full_name || 'Employee',
          leave_type_id: 'annual-leave',
          leave_type_name: 'Annual Leave',
          start_date: '2025-06-15',
          end_date: '2025-06-20',
          total_days: 5,
          reason: 'Family vacation',
          status: 'approved',
          approved_by: 'HR Manager',
          approved_at: '2025-05-22',
          created_at: '2025-05-20',
          updated_at: '2025-05-22'
        },
        {
          request_id: '2',
          employee_id: employeeId,
          employee_name: extendedUser?.employee_info?.full_name || 'Employee',
          leave_type_id: 'sick-leave',
          leave_type_name: 'Sick Leave',
          start_date: '2025-06-01',
          end_date: '2025-06-02',
          total_days: 2,
          reason: 'Medical appointment',
          status: 'pending',
          created_at: '2025-05-30',
          updated_at: '2025-05-30'
        }
      ];

      // Fetch leave requests for the employee
      try {
        const response = await apiGet<LeaveRequestsResponse>(
          `api/leave/requests?company_id=${companyId}&employee_id=${employeeId}`,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );

        if (response.extend && response.extend.leave_requests) {
          setLeaveRequests(response.extend.leave_requests);
        } else {
          setLeaveRequests(mockLeaveRequests);
        }
      } catch (error: any) {
        console.error('Error fetching leave requests:', error);
        setLeaveRequests(mockLeaveRequests);
      }

      // Fetch leave balances (mock for now)
      const mockLeaveBalances: LeaveBalance[] = [
        { leave_type: 'Annual Leave', total_days: 25, used_days: 10, remaining_days: 15 },
        { leave_type: 'Sick Leave', total_days: 12, used_days: 3, remaining_days: 9 },
        { leave_type: 'Personal Leave', total_days: 5, used_days: 1, remaining_days: 4 }
      ];

      setLeaveBalances(mockLeaveBalances);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching leave data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaveData();
  }, [companies, extendedUser?.employee_info?.employee_id]);

  const totalLeaveBalance = leaveBalances.reduce((sum, balance) => sum + balance.remaining_days, 0);
  const totalUsedLeave = leaveBalances.reduce((sum, balance) => sum + balance.used_days, 0);
  const pendingRequests = leaveRequests.filter(req => req.status === 'pending').length;

  const stats = [
    { title: 'Total Leave Balance', value: `${totalLeaveBalance} days`, change: '', changeType: 'positive' as const },
    { title: 'Used This Year', value: `${totalUsedLeave} days`, change: '', changeType: 'neutral' as const },
    { title: 'Pending Requests', value: pendingRequests.toString(), change: '', changeType: 'neutral' as const },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">My Leave</h1>
        <Link
          href="/dashboard/employee/leave/new"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Request Leave
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={loading}
          />
        ))}
      </div>

      {/* Leave Balances */}
      <DashboardCard title="Leave Balances" loading={loading}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {leaveBalances.map((balance, index) => (
            <div key={index} className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-900 mb-2">{balance.leave_type}</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total</span>
                  <span className="font-medium">{balance.total_days} days</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Used</span>
                  <span className="font-medium text-red-600">{balance.used_days} days</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Remaining</span>
                  <span className="font-medium text-green-600">{balance.remaining_days} days</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${(balance.used_days / balance.total_days) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </DashboardCard>

      {/* Available Leave Types */}
      <DashboardCard title="Available Leave Types" loading={loading}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {leaveTypes.map((leaveType) => (
            <div key={leaveType.leave_type_id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-900">{leaveType.name}</h3>
                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  {leaveType.code}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{leaveType.description}</p>
              <div className="space-y-2">
                <div className="flex items-center text-xs">
                  <span className={`w-2 h-2 rounded-full mr-2 ${leaveType.is_paid ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  <span className="text-gray-600">{leaveType.is_paid ? 'Paid Leave' : 'Unpaid Leave'}</span>
                </div>
                {leaveType.requires_approval && (
                  <div className="flex items-center text-xs">
                    <span className="w-2 h-2 rounded-full mr-2 bg-yellow-500"></span>
                    <span className="text-gray-600">Requires Approval</span>
                  </div>
                )}
                {leaveType.requires_documentation && (
                  <div className="flex items-center text-xs">
                    <span className="w-2 h-2 rounded-full mr-2 bg-purple-500"></span>
                    <span className="text-gray-600">Requires Documentation</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        {leaveTypes.length === 0 && !loading && (
          <div className="text-center py-8">
            <p className="text-gray-500">No leave types available</p>
          </div>
        )}
      </DashboardCard>

      {/* Leave Requests */}
      <DashboardCard title="My Leave Requests" loading={loading}>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {['Leave Type', 'Start Date', 'End Date', 'Days', 'Reason', 'Status', 'Applied Date'].map((header) => (
                  <th 
                    key={header}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {leaveRequests.length > 0 ? (
                leaveRequests.map((request) => (
                  <tr key={request.request_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{request.leave_type_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{request.start_date}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{request.end_date}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{request.total_days}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 max-w-xs truncate" title={request.reason}>
                        {request.reason}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          request.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : request.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{new Date(request.created_at).toLocaleDateString()}</div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    {loading ? 'Loading...' : 'No leave requests found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </DashboardCard>
    </div>
  );
};

export default EmployeeLeave;
