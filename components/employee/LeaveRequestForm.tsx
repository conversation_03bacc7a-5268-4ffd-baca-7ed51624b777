'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiPost, apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';

interface LeaveRequestData {
  leave_type: string;
  start_date: string;
  end_date: string;
  reason: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}

interface ExtendedUser {
  employee_id: string;
  employee_info: {
    employee_id: string;
    full_name: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

const LeaveRequestForm = () => {
  const router = useRouter();
  const { user, companies } = useAuth();
  const extendedUser = user as ExtendedUser;
  const [loading, setLoading] = useState(false);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(true);
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveType | null>(null);
  const [formData, setFormData] = useState<LeaveRequestData>({
    leave_type: '',
    start_date: '',
    end_date: '',
    reason: '',
    emergency_contact: '',
    emergency_phone: ''
  });

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      if (!companies || companies.length === 0) {
        setLoadingTypes(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.error('Error fetching leave types:', error);
    } finally {
      setLoadingTypes(false);
    }
  };

  useEffect(() => {
    fetchLeaveTypes();
  }, [companies]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'leave_type') {
      const selectedType = leaveTypes.find(type => type.leave_type_id === value);
      setSelectedLeaveType(selectedType || null);
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateDays = () => {
    if (formData.start_date && formData.end_date) {
      const start = new Date(formData.start_date);
      const end = new Date(formData.end_date);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays;
    }
    return 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!companies || companies.length === 0 || !extendedUser?.employee_info?.employee_id) {
      alert('Employee information not available');
      return;
    }

    setLoading(true);

    try {
      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies[0].company_id;
      const employeeId = extendedUser.employee_info.employee_id;

      const requestData = {
        employee_id: employeeId,
        company_id: companyId,
        leave_type: formData.leave_type,
        start_date: formData.start_date,
        end_date: formData.end_date,
        days_requested: calculateDays(),
        reason: formData.reason,
        emergency_contact: formData.emergency_contact,
        emergency_phone: formData.emergency_phone
      };

      // TODO: Replace with actual API call when endpoint is available
      console.log('Leave request data:', requestData);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Leave request submitted successfully!');
      router.push('/dashboard/employee/leave');
    } catch (error) {
      console.error('Error submitting leave request:', error);
      alert('Failed to submit leave request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Request Leave</h1>
        <button
          onClick={() => router.back()}
          className="text-gray-600 hover:text-gray-900 text-sm font-medium"
        >
          ← Back to Leave
        </button>
      </div>

      <DashboardCard title="Leave Request Form">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Leave Type */}
            <div>
              <label htmlFor="leave_type" className="block text-sm font-medium text-gray-700 mb-2">
                Leave Type *
              </label>
              <select
                id="leave_type"
                name="leave_type"
                value={formData.leave_type}
                onChange={handleInputChange}
                required
                disabled={loadingTypes}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              >
                <option value="">{loadingTypes ? 'Loading...' : 'Select leave type'}</option>
                {leaveTypes.map((type) => (
                  <option key={type.leave_type_id} value={type.leave_type_id}>
                    {type.name} ({type.code})
                  </option>
                ))}
              </select>

              {/* Leave Type Information */}
              {selectedLeaveType && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">{selectedLeaveType.name}</h4>
                  <p className="text-sm text-blue-800 mb-2">{selectedLeaveType.description}</p>
                  <div className="flex flex-wrap gap-2">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedLeaveType.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {selectedLeaveType.is_paid ? 'Paid' : 'Unpaid'}
                    </span>
                    {selectedLeaveType.requires_approval && (
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Requires Approval
                      </span>
                    )}
                    {selectedLeaveType.requires_documentation && (
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                        Documentation Required
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Days Requested (calculated) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Days Requested
              </label>
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700">
                {calculateDays()} days
              </div>
            </div>

            {/* Start Date */}
            <div>
              <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-2">
                Start Date *
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                required
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* End Date */}
            <div>
              <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
                End Date *
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                required
                min={formData.start_date || new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Emergency Contact */}
            <div>
              <label htmlFor="emergency_contact" className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Name
              </label>
              <input
                type="text"
                id="emergency_contact"
                name="emergency_contact"
                value={formData.emergency_contact}
                onChange={handleInputChange}
                placeholder="Contact person during leave"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Emergency Phone */}
            <div>
              <label htmlFor="emergency_phone" className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Phone
              </label>
              <input
                type="tel"
                id="emergency_phone"
                name="emergency_phone"
                value={formData.emergency_phone}
                onChange={handleInputChange}
                placeholder="Phone number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Reason */}
          <div>
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Leave *
            </label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleInputChange}
              required
              rows={4}
              placeholder="Please provide a detailed reason for your leave request..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Submitting...' : 'Submit Request'}
            </button>
          </div>
        </form>
      </DashboardCard>
    </div>
  );
};

export default LeaveRequestForm;
