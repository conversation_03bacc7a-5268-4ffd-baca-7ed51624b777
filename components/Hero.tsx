import React from 'react';
import Link from 'next/link';

const Hero = () => {
  return (
    <section className="bg-gradient-to-b from-background to-background-dark py-16 md:py-24">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-dark mb-6">
              Simplify Your <span className="text-primary">Attendance Management</span> with KaziSync
            </h1>
            <p className="text-lg text-secondary mb-8 max-w-lg">
              A modern, multi-tenant web-based system for organizations to efficiently manage employee attendance, shifts, leaves, and biometric integration.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/signup" className="btn-primary text-center px-8 py-3">
                Get Started
              </Link>
              <Link href="#features" className="btn-outline text-center px-8 py-3">
                Learn More
              </Link>
            </div>
            <div className="mt-8 flex items-center text-sm text-secondary">
              <svg
                className="h-5 w-5 mr-2 text-success"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span>No credit card required for trial</span>
            </div>
          </div>
          <div className="relative animate-fade-in">
            <div className="bg-white rounded-lg shadow-xl p-4 md:p-8 relative z-10">
              <div className="aspect-w-16 aspect-h-9 bg-background-dark rounded-lg flex items-center justify-center">
                {/* Placeholder for hero image/illustration */}
                <div className="text-center p-12">
                  <svg
                    className="h-24 w-24 mx-auto text-primary opacity-80"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                    />
                  </svg>
                  <p className="mt-4 text-secondary-dark font-medium">Dashboard Preview</p>
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -bottom-4 -right-4 w-64 h-64 bg-primary-light opacity-10 rounded-full z-0"></div>
            <div className="absolute -top-4 -left-4 w-32 h-32 bg-primary opacity-10 rounded-full z-0"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
