'use client';

import React, { useState } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

// Types for results
interface CreatedUser {
  department: string | null;
  employee_id: string;
  employee_name: string;
  generated_password: string;
  login_username: string;
  role: string;
  username: string;
}

interface SkippedUser {
  employee_id: string;
  employee_name: string;
  existing_username: string;
  reason: string;
}

interface FailedUser {
  employee_id: string;
  employee_name: string;
  error: string;
  reason: string;
}

interface AccountCreationResult {
  created_users: CreatedUser[];
  skipped_users: SkippedUser[];
  failed_users: FailedUser[];
  success: boolean;
  summary: {
    created: number;
    failed: number;
    skipped: number;
    total_requested?: number;
    total_employees?: number;
  };
}

interface SingleAccountResult {
  employee_info: {
    department: string;
    employee_id: string;
    name: string;
  };
  generated_password: string;
  login_username: string;
  message: string;
  success: boolean;
  user: {
    created_at: string;
    email: string;
    employee: {
      department_id: string;
      first_name: string;
      full_name: string;
      id: string;
      last_name: string;
    };
    employee_id: string;
    first_name: string;
    full_name: string;
    id: string;
    is_active: boolean;
    last_name: string;
    phone_number: string | null;
    role: string;
    updated_at: string | null;
    username: string;
  };
}

interface AccountCreationResultsProps {
  result: AccountCreationResult | SingleAccountResult;
  onClear: () => void;
}

const AccountCreationResults: React.FC<AccountCreationResultsProps> = ({ result, onClear }) => {
  const [activeTab, setActiveTab] = useState<'summary' | 'created' | 'skipped' | 'failed'>('summary');
  const [showPasswords, setShowPasswords] = useState(false);

  // Check if it's a single account result
  const isSingleResult = 'user' in result;

  // Convert single result to bulk format for consistent display
  const bulkResult: AccountCreationResult = isSingleResult ? {
    created_users: [{
      department: result.employee_info.department,
      employee_id: result.employee_info.employee_id,
      employee_name: result.employee_info.name,
      generated_password: result.generated_password,
      login_username: result.login_username,
      role: result.user.role,
      username: result.user.username
    }],
    skipped_users: [],
    failed_users: [],
    success: result.success,
    summary: {
      created: 1,
      failed: 0,
      skipped: 0,
      total_requested: 1
    }
  } : result as AccountCreationResult;

  const { created_users, skipped_users, failed_users, summary } = bulkResult;

  // Copy to clipboard function
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // You could add a toast notification here
      console.log('Copied to clipboard:', text);
    });
  };

  // Export results as CSV
  const exportToCSV = () => {
    const headers = ['Employee Name', 'Username', 'Login Username', 'Password', 'Department', 'Role'];
    const rows = created_users.map(user => [
      user.employee_name,
      user.username,
      user.login_username,
      user.generated_password,
      user.department || 'N/A',
      user.role
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `employee_accounts_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <DashboardCard title="Account Creation Results">
      <div className="space-y-6">
        {/* Header with Actions */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium text-secondary-dark">
              {isSingleResult ? 'Single Account Created' : 'Bulk Account Creation Results'}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {isSingleResult 
                ? 'Account has been successfully created for the employee'
                : 'Summary of account creation process for multiple employees'
              }
            </p>
          </div>
          <div className="flex gap-2">
            {created_users.length > 0 && (
              <>
                <button
                  onClick={() => setShowPasswords(!showPasswords)}
                  className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
                >
                  {showPasswords ? 'Hide' : 'Show'} Passwords
                </button>
                <button
                  onClick={exportToCSV}
                  className="px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-md hover:bg-green-100"
                >
                  Export CSV
                </button>
              </>
            )}
            <button
              onClick={onClear}
              className="px-3 py-2 text-sm font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{summary.created}</div>
              <div className="text-sm text-green-700">Created</div>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{summary.skipped}</div>
              <div className="text-sm text-yellow-700">Skipped</div>
            </div>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{summary.failed}</div>
              <div className="text-sm text-red-700">Failed</div>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {summary.total_requested || summary.total_employees || summary.created + summary.skipped + summary.failed}
              </div>
              <div className="text-sm text-blue-700">Total</div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('summary')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'summary'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Summary
            </button>
            {created_users.length > 0 && (
              <button
                onClick={() => setActiveTab('created')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'created'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Created ({created_users.length})
              </button>
            )}
            {skipped_users.length > 0 && (
              <button
                onClick={() => setActiveTab('skipped')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'skipped'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Skipped ({skipped_users.length})
              </button>
            )}
            {failed_users.length > 0 && (
              <button
                onClick={() => setActiveTab('failed')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'failed'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Failed ({failed_users.length})
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'summary' && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Process Summary</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>✅ {summary.created} account{summary.created !== 1 ? 's' : ''} created successfully</li>
                  {summary.skipped > 0 && (
                    <li>⏭️ {summary.skipped} employee{summary.skipped !== 1 ? 's' : ''} skipped (already have accounts)</li>
                  )}
                  {summary.failed > 0 && (
                    <li>❌ {summary.failed} account{summary.failed !== 1 ? 's' : ''} failed to create</li>
                  )}
                </ul>
              </div>
              
              {created_users.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Important Notes</h4>
                  <ul className="space-y-1 text-sm text-blue-700">
                    <li>• Login credentials have been generated for all new accounts</li>
                    <li>• Passwords will be sent to employees via email (when email system is configured)</li>
                    <li>• Employees can use their username or login_username to sign in</li>
                    <li>• All accounts are created with "employee" role by default</li>
                  </ul>
                </div>
              )}
            </div>
          )}

          {activeTab === 'created' && created_users.length > 0 && (
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Username
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Login Username
                      </th>
                      {showPasswords && (
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Password
                        </th>
                      )}
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Department
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {created_users.map((user, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {user.employee_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center space-x-2">
                            <span>{user.username}</span>
                            <button
                              onClick={() => copyToClipboard(user.username)}
                              className="text-gray-400 hover:text-gray-600"
                              title="Copy username"
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center space-x-2">
                            <span className="font-mono">{user.login_username}</span>
                            <button
                              onClick={() => copyToClipboard(user.login_username)}
                              className="text-gray-400 hover:text-gray-600"
                              title="Copy login username"
                            >
                              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </td>
                        {showPasswords && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center space-x-2">
                              <span className="font-mono bg-gray-100 px-2 py-1 rounded">{user.generated_password}</span>
                              <button
                                onClick={() => copyToClipboard(user.generated_password)}
                                className="text-gray-400 hover:text-gray-600"
                                title="Copy password"
                              >
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        )}
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.department || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {user.role}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'skipped' && skipped_users.length > 0 && (
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Existing Username
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reason
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {skipped_users.map((user, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {user.employee_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.existing_username}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.reason}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'failed' && failed_users.length > 0 && (
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Error
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reason
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {failed_users.map((user, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {user.employee_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                          {user.error}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.reason}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardCard>
  );
};

export default AccountCreationResults;
