'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// TypeScript interfaces for the API response
interface TrendApplications {
  by_status: {
    approved: number;
    cancelled: number;
    pending: number;
    rejected: number;
  };
  total: number;
  unique_applicants: number;
}

interface TrendDecisions {
  approval_rate: number;
  approved: number;
  avg_processing_days: number;
  rejected: number;
  total: number;
}

interface TrendUtilization {
  avg_days_per_leave: number;
  leave_instances: number;
  total_days_taken: number;
  unique_employees_on_leave: number;
}

interface TrendInsights {
  busiest_department: string | null;
  top_leave_type: string | null;
}

interface TrendPeriod {
  applications_received: TrendApplications;
  decisions_made: TrendDecisions;
  insights: TrendInsights;
  leave_utilization: TrendUtilization;
  period: string;
  period_name: string;
}

interface PeakPeriods {
  best_approval_rate: {
    period: string;
    rate: number;
  };
  highest_applications_received: {
    count: number;
    period: string;
  };
  highest_leave_utilization: {
    days: number;
    period: string;
  };
  most_decisions_made: {
    count: number;
    period: string;
  };
}

interface TrendAverages {
  avg_applications_received_per_period: number;
  avg_approval_rate: number;
  avg_decisions_made_per_period: number;
  avg_leave_utilization_per_period: number;
}

interface GrowthAnalysis {
  applications_received_growth: number;
  approval_rate_change: number;
  decisions_made_growth: number;
  leave_utilization_growth: number;
}

interface TrendInsightsData {
  averages: TrendAverages;
  growth_analysis: GrowthAnalysis;
  peak_periods: PeakPeriods;
}

interface TrendsData {
  period_type: string;
  periods_analyzed: number;
  trend_insights: TrendInsightsData;
  trends: TrendPeriod[];
}

const LeaveTrendsAnalytics: React.FC = () => {
  const { companies } = useAuth();
  const [trendsData, setTrendsData] = useState<TrendsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [customPeriods, setCustomPeriods] = useState({
    months_back: 12,
    weeks_back: 12,
    years_back: 3
  });

  // Fetch trends data
  const fetchTrends = async () => {
    if (!companies || companies.length === 0) return;

    setLoading(true);
    setError('');

    try {
      const companyId = companies[0].company_id;
      let url = `/api/leave/analytics/trends?company_id=${companyId}&period=${selectedPeriod}`;

      // Add custom period parameters
      if (selectedPeriod === 'monthly' && customPeriods.months_back !== 12) {
        url += `&months_back=${customPeriods.months_back}`;
      } else if (selectedPeriod === 'week' && customPeriods.weeks_back !== 12) {
        url += `&weeks_back=${customPeriods.weeks_back}`;
      } else if (selectedPeriod === 'year' && customPeriods.years_back !== 3) {
        url += `&years_back=${customPeriods.years_back}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        console.log('Trends API Response:', result.data); // Debug log
        setTrendsData(result.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching trends data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch trends data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrends();
  }, [companies, selectedPeriod, customPeriods]);

  // Chart configuration
  const getChartOptions = (title: string) => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: title,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  });

  // Prepare chart data
  const getApplicationsChartData = () => {
    if (!trendsData) return null;

    const labels = trendsData.trends.map(trend => trend.period_name);
    
    return {
      labels,
      datasets: [
        {
          label: 'Total Applications',
          data: trendsData.trends.map(trend => trend.applications_received.total),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
        },
        {
          label: 'Approved',
          data: trendsData.trends.map(trend => trend.applications_received.by_status.approved),
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: false,
        },
        {
          label: 'Rejected',
          data: trendsData.trends.map(trend => trend.applications_received.by_status.rejected),
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: false,
        },
        {
          label: 'Pending',
          data: trendsData.trends.map(trend => trend.applications_received.by_status.pending),
          borderColor: 'rgb(245, 158, 11)',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          fill: false,
        }
      ],
    };
  };

  const getApprovalRateChartData = () => {
    if (!trendsData) return null;

    const labels = trendsData.trends.map(trend => trend.period_name);
    
    return {
      labels,
      datasets: [
        {
          label: 'Approval Rate (%)',
          data: trendsData.trends.map(trend => trend.decisions_made.approval_rate),
          borderColor: 'rgb(168, 85, 247)',
          backgroundColor: 'rgba(168, 85, 247, 0.1)',
          fill: true,
        }
      ],
    };
  };

  const getUtilizationChartData = () => {
    if (!trendsData) return null;

    const labels = trendsData.trends.map(trend => trend.period_name);
    
    return {
      labels,
      datasets: [
        {
          label: 'Total Days Taken',
          data: trendsData.trends.map(trend => trend.leave_utilization.total_days_taken),
          backgroundColor: 'rgba(34, 197, 94, 0.8)',
        },
        {
          label: 'Leave Instances',
          data: trendsData.trends.map(trend => trend.leave_utilization.leave_instances),
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
        }
      ],
    };
  };

  const formatGrowthPercentage = (value: number) => {
    const sign = value > 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (value: number) => {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Leave Trends Analytics</h2>
          <p className="text-sm text-gray-600 mt-1">
            Time series analysis of leave management trends
          </p>
        </div>
      </div>

      {/* Filters */}
      <DashboardCard title="Period Selection & Filters">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Period Type */}
          <div>
            <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-1">
              Period Type
            </label>
            <select
              id="period"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="monthly">Monthly</option>
              <option value="week">Weekly</option>
              <option value="year">Yearly</option>
            </select>
          </div>

          {/* Custom Period Controls */}
          {selectedPeriod === 'monthly' && (
            <div>
              <label htmlFor="months_back" className="block text-sm font-medium text-gray-700 mb-1">
                Months Back
              </label>
              <input
                type="number"
                id="months_back"
                min="1"
                max="60"
                value={customPeriods.months_back}
                onChange={(e) => setCustomPeriods(prev => ({ ...prev, months_back: parseInt(e.target.value) || 12 }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          )}

          {selectedPeriod === 'week' && (
            <div>
              <label htmlFor="weeks_back" className="block text-sm font-medium text-gray-700 mb-1">
                Weeks Back
              </label>
              <input
                type="number"
                id="weeks_back"
                min="1"
                max="104"
                value={customPeriods.weeks_back}
                onChange={(e) => setCustomPeriods(prev => ({ ...prev, weeks_back: parseInt(e.target.value) || 12 }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          )}

          {selectedPeriod === 'year' && (
            <div>
              <label htmlFor="years_back" className="block text-sm font-medium text-gray-700 mb-1">
                Years Back
              </label>
              <input
                type="number"
                id="years_back"
                min="1"
                max="10"
                value={customPeriods.years_back}
                onChange={(e) => setCustomPeriods(prev => ({ ...prev, years_back: parseInt(e.target.value) || 3 }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          )}

          {/* Refresh Button */}
          <div className="flex items-end">
            <button
              onClick={fetchTrends}
              disabled={loading}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 transition-colors font-medium"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Period Info */}
        {trendsData && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>Analysis:</strong> {trendsData.periods_analyzed} {trendsData.period_type} periods analyzed
            </p>
          </div>
        )}
      </DashboardCard>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Trends Content */}
      {trendsData ? (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <DashboardCard title="Avg Applications/Period">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {trendsData.trend_insights.averages.avg_applications_received_per_period.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Growth: <span className={getGrowthColor(trendsData.trend_insights.growth_analysis.applications_received_growth)}>
                    {formatGrowthPercentage(trendsData.trend_insights.growth_analysis.applications_received_growth)}
                  </span>
                </div>
              </div>
            </DashboardCard>

            <DashboardCard title="Avg Approval Rate">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {trendsData.trend_insights.averages.avg_approval_rate.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Change: <span className={getGrowthColor(trendsData.trend_insights.growth_analysis.approval_rate_change)}>
                    {formatGrowthPercentage(trendsData.trend_insights.growth_analysis.approval_rate_change)}
                  </span>
                </div>
              </div>
            </DashboardCard>

            <DashboardCard title="Avg Decisions/Period">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {trendsData.trend_insights.averages.avg_decisions_made_per_period.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Growth: <span className={getGrowthColor(trendsData.trend_insights.growth_analysis.decisions_made_growth)}>
                    {formatGrowthPercentage(trendsData.trend_insights.growth_analysis.decisions_made_growth)}
                  </span>
                </div>
              </div>
            </DashboardCard>

            <DashboardCard title="Avg Utilization/Period">
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {trendsData.trend_insights.averages.avg_leave_utilization_per_period.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Growth: <span className={getGrowthColor(trendsData.trend_insights.growth_analysis.leave_utilization_growth)}>
                    {formatGrowthPercentage(trendsData.trend_insights.growth_analysis.leave_utilization_growth)}
                  </span>
                </div>
              </div>
            </DashboardCard>
          </div>

          {/* Peak Periods */}
          <DashboardCard title="Peak Periods Analysis">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-green-900">
                  {trendsData.trend_insights.peak_periods.best_approval_rate.rate.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">Best Approval Rate</div>
                <div className="text-xs text-green-600 mt-1">
                  {trendsData.trend_insights.peak_periods.best_approval_rate.period}
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-blue-900">
                  {trendsData.trend_insights.peak_periods.highest_applications_received.count}
                </div>
                <div className="text-sm text-blue-700">Most Applications</div>
                <div className="text-xs text-blue-600 mt-1">
                  {trendsData.trend_insights.peak_periods.highest_applications_received.period}
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-purple-900">
                  {trendsData.trend_insights.peak_periods.most_decisions_made.count}
                </div>
                <div className="text-sm text-purple-700">Most Decisions</div>
                <div className="text-xs text-purple-600 mt-1">
                  {trendsData.trend_insights.peak_periods.most_decisions_made.period}
                </div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-orange-900">
                  {trendsData.trend_insights.peak_periods.highest_leave_utilization.days}
                </div>
                <div className="text-sm text-orange-700">Most Days Taken</div>
                <div className="text-xs text-orange-600 mt-1">
                  {trendsData.trend_insights.peak_periods.highest_leave_utilization.period}
                </div>
              </div>
            </div>
          </DashboardCard>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Applications Trend Chart */}
            <DashboardCard title="Applications Trend">
              <div className="h-80">
                {getApplicationsChartData() && (
                  <Line
                    data={getApplicationsChartData()!}
                    options={getChartOptions('Applications Over Time')}
                  />
                )}
              </div>
            </DashboardCard>

            {/* Approval Rate Trend Chart */}
            <DashboardCard title="Approval Rate Trend">
              <div className="h-80">
                {getApprovalRateChartData() && (
                  <Line
                    data={getApprovalRateChartData()!}
                    options={getChartOptions('Approval Rate Over Time')}
                  />
                )}
              </div>
            </DashboardCard>
          </div>

          {/* Utilization Chart */}
          <DashboardCard title="Leave Utilization Trend">
            <div className="h-80">
              {getUtilizationChartData() && (
                <Bar
                  data={getUtilizationChartData()!}
                  options={getChartOptions('Leave Utilization Over Time')}
                />
              )}
            </div>
          </DashboardCard>

          {/* Detailed Trends Table */}
          <DashboardCard title="Detailed Trends Data">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50/80">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Period
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approved
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approval Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Days Taken
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unique Applicants
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Top Department
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {trendsData.trends.map((trend, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{trend.period_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{trend.applications_received.total}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{trend.applications_received.by_status.approved}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">{trend.decisions_made.approval_rate.toFixed(1)}%</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{trend.leave_utilization.total_days_taken}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{trend.applications_received.unique_applicants}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{trend.insights.busiest_department || 'N/A'}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </DashboardCard>
        </>
      ) : (
        /* Loading or No Data State */
        <DashboardCard title="Trends Analytics">
          <div className="text-center py-8">
            <p className="text-gray-500">
              {loading ? 'Loading trends data...' : 'No trends data available'}
            </p>
          </div>
        </DashboardCard>
      )}
    </div>
  );
};

export default LeaveTrendsAnalytics;
