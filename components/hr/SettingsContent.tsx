'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

const SettingsContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Settings</h1>
        <div className="flex space-x-2">
          <button className="btn-primary py-1 px-3 text-sm">Save Changes</button>
        </div>
      </div>
      
      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Settings</span>
      </div>
      
      <DashboardCard title="Settings">
        <div className="py-8 text-center">
          <p className="text-secondary">Settings features will be implemented in a future update.</p>
          <p className="text-secondary mt-2">This page will include company settings, user preferences, and system configurations.</p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default SettingsContent;
