'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';

interface PeriodInfo {
  period: string;
  year: number;
  month?: number;
  week?: number;
  start_date: string;
  end_date: string;
}

interface ApplicationStats {
  approved: number;
  cancelled: number;
  pending: number;
  rejected: number;
  total: number;
}

interface DaysStats {
  avg_processing_days?: number;
  total_days_taken?: number;
  avg_leave_duration?: number;
  total_days_approved?: number;
  total_days_requested?: number;
}

interface RatesStats {
  approval_rate: number;
  rejection_rate: number;
  cancellation_rate?: number;
}

interface PeriodStatistics {
  applications: ApplicationStats;
  days: DaysStats;
  employees: {
    unique_applicants: number;
  };
  rates: RatesStats;
}

interface AllTimeStatistics {
  approval_rate: number;
  approved_applications: number;
  pending_applications: number;
  total_applications: number;
}

interface DepartmentBreakdown {
  department_id: string;
  department_name: string;
  applications: ApplicationStats;
  days: DaysStats;
  employees: {
    unique_applicants: number;
  };
  rates: RatesStats;
  timeline: {
    first_application: string;
    last_application: string;
  };
}

interface LeaveTypeBreakdown {
  applications: number;
  average_days: number;
  code: string;
  leave_type: string;
  total_days: number;
}

interface StatusBreakdown {
  avg_days: number;
  count: number;
  percentage: number;
  status: string;
  total_days: number;
  unique_employees: number;
}

interface TopApplicant {
  employee_id: string;
  employee_name: string;
  department: string;
  applications: {
    approved: number;
    total: number;
  };
  approval_rate: number;
  days: {
    total_approved: number;
    total_requested: number;
  };
}

interface LeavePattern {
  by_day_of_week: Array<{
    day_of_week: number;
    day_name: string;
    applications: number;
    avg_duration: number;
  }>;
  monthly_distribution: Array<{
    month: number;
    month_name: string;
    applications: number;
    avg_duration: number;
  }>;
}

interface Insights {
  avg_leave_duration_company: number;
  busiest_department: string;
  highest_approval_rate_dept: string;
  most_days_taken_dept: string;
}

interface AnalyticsData {
  period_info: PeriodInfo;
  period_statistics: PeriodStatistics;
  all_time_statistics: AllTimeStatistics;
  department_breakdown: DepartmentBreakdown[];
  leave_type_breakdown: LeaveTypeBreakdown[];
  status_breakdown: StatusBreakdown[];
  top_applicants: TopApplicant[];
  leave_patterns: LeavePattern;
  insights: Insights;
}

interface AnalyticsResponse {
  data: AnalyticsData;
  success: boolean;
}

const LeaveAnalytics: React.FC = () => {
  const { companies } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Filter states
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedWeek, setSelectedWeek] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      if (!companies || companies.length === 0) {
        setLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading(true);
      setError('');

      // Build query parameters
      const params = new URLSearchParams({
        company_id: companyId,
        period: selectedPeriod,
        page: currentPage.toString(),
        per_page: perPage.toString()
      });

      // Add period-specific parameters
      if (selectedPeriod === 'month') {
        params.append('year', selectedYear.toString());
        params.append('month', selectedMonth.toString());
      } else if (selectedPeriod === 'week') {
        params.append('year', selectedYear.toString());
        params.append('week', selectedWeek.toString());
      } else if (selectedPeriod === 'quarter') {
        params.append('year', selectedYear.toString());
        params.append('month', selectedMonth.toString());
      } else if (selectedPeriod === 'year') {
        params.append('year', selectedYear.toString());
      }

      const response = await apiGet<AnalyticsResponse>(
        `api/leave/analytics/overview?${params.toString()}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.data) {
        setAnalyticsData(response.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      console.error('Error fetching analytics:', error);
      setError(error.message || 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [companies, selectedPeriod, selectedYear, selectedMonth, selectedWeek, currentPage, perPage]);

  // Generate year options
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  // Generate month options
  const monthOptions = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  // Generate week options (1-52)
  const weekOptions = Array.from({ length: 52 }, (_, i) => i + 1);

  // Calculate key metrics for stats cards
  const getStatsCards = () => {
    if (!analyticsData) return [];

    const { period_statistics, all_time_statistics } = analyticsData;

    return [
      {
        title: 'Total Applications',
        value: period_statistics.applications.total.toString(),
        change: '',
        changeType: 'neutral' as const
      },
      {
        title: 'Approval Rate',
        value: `${period_statistics.rates.approval_rate}%`,
        change: '',
        changeType: 'positive' as const
      },
      {
        title: 'Unique Applicants',
        value: period_statistics.employees.unique_applicants.toString(),
        change: '',
        changeType: 'neutral' as const
      },
      {
        title: 'Avg Processing Days',
        value: period_statistics.days.avg_processing_days?.toFixed(1) || '0',
        change: '',
        changeType: 'neutral' as const
      }
    ];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Leave Analytics & Reports</h2>
          <p className="text-sm text-gray-600 mt-1">
            Comprehensive insights into leave patterns and trends
          </p>
        </div>
      </div>

      {/* Filters */}
      <DashboardCard title="Filters & Period Selection">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Period Type */}
          <div>
            <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-1">
              Period Type
            </label>
            <select
              id="period"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="week">Week</option>
              <option value="month">Month</option>
              <option value="quarter">Quarter</option>
              <option value="year">Year</option>
            </select>
          </div>

          {/* Year */}
          <div>
            <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
              Year
            </label>
            <select
              id="year"
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              {yearOptions.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          {/* Month (for month/quarter periods) */}
          {(selectedPeriod === 'month' || selectedPeriod === 'quarter') && (
            <div>
              <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-1">
                {selectedPeriod === 'quarter' ? 'Quarter Start Month' : 'Month'}
              </label>
              <select
                id="month"
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                {monthOptions.map(month => (
                  <option key={month.value} value={month.value}>{month.label}</option>
                ))}
              </select>
            </div>
          )}

          {/* Week (for week period) */}
          {selectedPeriod === 'week' && (
            <div>
              <label htmlFor="week" className="block text-sm font-medium text-gray-700 mb-1">
                Week Number
              </label>
              <select
                id="week"
                value={selectedWeek}
                onChange={(e) => setSelectedWeek(parseInt(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                {weekOptions.map(week => (
                  <option key={week} value={week}>Week {week}</option>
                ))}
              </select>
            </div>
          )}

          {/* Refresh Button */}
          <div className="flex items-end">
            <button
              onClick={fetchAnalytics}
              disabled={loading}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 transition-colors font-medium"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Period Info */}
        {analyticsData?.period_info && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>Analysis Period:</strong> {formatDate(analyticsData.period_info.start_date)} to {formatDate(analyticsData.period_info.end_date)}
              {analyticsData.period_info.period === 'month' && ` (${monthOptions.find(m => m.value === analyticsData.period_info.month)?.label} ${analyticsData.period_info.year})`}
              {analyticsData.period_info.period === 'week' && ` (Week ${analyticsData.period_info.week}, ${analyticsData.period_info.year})`}
            </p>
          </div>
        )}
      </DashboardCard>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      {analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getStatsCards().map((stat, index) => (
            <DashboardStats
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              loading={loading}
            />
          ))}
        </div>
      )}

      {/* Analytics Content */}
      {analyticsData && (
        <>
          {/* Insights */}
          <DashboardCard title="Key Insights">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-blue-900">
                  {analyticsData.insights.avg_leave_duration_company} days
                </div>
                <div className="text-sm text-blue-700">Average Leave Duration</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-green-900">
                  {analyticsData.insights.busiest_department}
                </div>
                <div className="text-sm text-green-700">Busiest Department</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-purple-900">
                  {analyticsData.insights.highest_approval_rate_dept}
                </div>
                <div className="text-sm text-purple-700">Highest Approval Rate</div>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="text-lg font-semibold text-orange-900">
                  {analyticsData.insights.most_days_taken_dept}
                </div>
                <div className="text-sm text-orange-700">Most Days Taken</div>
              </div>
            </div>
          </DashboardCard>

          {/* Status Breakdown */}
          <DashboardCard title="Application Status Breakdown">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {analyticsData.status_breakdown.map((status, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status.status)}`}>
                      {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                    </span>
                    <span className="text-lg font-bold text-gray-900">{status.percentage}%</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Applications:</span>
                      <span className="font-medium">{status.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Days:</span>
                      <span className="font-medium">{status.total_days}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Days:</span>
                      <span className="font-medium">{status.avg_days}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Employees:</span>
                      <span className="font-medium">{status.unique_employees}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </DashboardCard>

          {/* Leave Type Breakdown */}
          <DashboardCard title="Leave Type Analysis">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50/80">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Leave Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Code
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Days
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Average Days
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.leave_type_breakdown.map((leaveType, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{leaveType.leave_type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {leaveType.code}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{leaveType.applications}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{leaveType.total_days}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{leaveType.average_days}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </DashboardCard>

          {/* Department Breakdown */}
          <DashboardCard title="Department Analysis">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50/80">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approval Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Days
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unique Applicants
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.department_breakdown.map((dept, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{dept.department_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{dept.applications.total}</div>
                        <div className="text-xs text-gray-500">
                          {dept.applications.approved} approved, {dept.applications.pending} pending
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">{dept.rates.approval_rate}%</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{dept.days.total_days_approved || 0}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{dept.days.avg_leave_duration || 0} days</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{dept.employees.unique_applicants}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </DashboardCard>

          {/* Top Applicants */}
          <DashboardCard title="Top Applicants">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50/80">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Approval Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Days Approved
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Days Requested
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.top_applicants.map((applicant, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{applicant.employee_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{applicant.department}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{applicant.applications.total}</div>
                        <div className="text-xs text-gray-500">{applicant.applications.approved} approved</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">{applicant.approval_rate}%</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{applicant.days.total_approved}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{applicant.days.total_requested}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </DashboardCard>

          {/* Leave Patterns */}
          {analyticsData.leave_patterns.by_day_of_week.length > 0 && (
            <DashboardCard title="Leave Patterns by Day of Week">
              <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                {analyticsData.leave_patterns.by_day_of_week.map((day, index) => (
                  <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-900">{day.day_name}</div>
                    <div className="text-2xl font-bold text-blue-600 mt-2">{day.applications}</div>
                    <div className="text-xs text-gray-500">applications</div>
                    <div className="text-sm text-gray-600 mt-1">{day.avg_duration} avg days</div>
                  </div>
                ))}
              </div>
            </DashboardCard>
          )}
        </>
      )}
    </div>
  );
};

export default LeaveAnalytics;
