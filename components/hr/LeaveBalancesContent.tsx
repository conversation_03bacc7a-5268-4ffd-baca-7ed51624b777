'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, apiPost, apiPut, apiDelete } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import LeaveBalancesTable from './LeaveBalancesTable';
import LeaveBalanceModal from './LeaveBalanceModal';

interface LeaveBalance {
  balance_id: string;
  employee_id: string;
  employee_name: string;
  leave_type_id: string;
  leave_type_name: string;
  leave_type_code: string;
  total_days: number;           // Total days allocated for this leave type
  used_days: number;            // Days already used/taken
  available_days: number;       // Days available to use (calculated by API)
  pending_days: number;         // Days in pending leave requests
  carried_over_days: number;    // Days carried over from previous year
  year: number;
  created_at: string;
  updated_at: string;
}

interface LeaveBalancesResponse {
  code: number;
  extend: {
    leave_balances: LeaveBalance[];
    total_balances: number;
    year: number;
  };
  msg: string;
}

interface Employee {
  employee_id: string;
  full_name: string;
  email: string;
  position: string;
  department_name?: string;
}

interface EmployeesResponse {
  employees: Employee[];
  pagination: {
    has_next: boolean;
    has_prev: boolean;
    page: number;
    pages: number;
    per_page: number;
    total_count: number;
  };
  success: boolean;
}

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}

interface LeaveBalanceFormData {
  employee_id: string;
  leave_type_id: string;
  year: number;
  total_days: number;
  carried_over_days: number;
}

const LeaveBalancesContent: React.FC = () => {
  const { companies } = useAuth();
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingBalance, setEditingBalance] = useState<LeaveBalance | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [selectedLeaveType, setSelectedLeaveType] = useState('');
  
  const [formData, setFormData] = useState<LeaveBalanceFormData>({
    employee_id: '',
    leave_type_id: '',
    year: new Date().getFullYear(),
    total_days: 0,
    carried_over_days: 0
  });

  // Fetch leave balances
  const fetchLeaveBalances = async () => {
    try {
      if (!companies || companies.length === 0) {
        setLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading(true);

      const response = await apiGet<LeaveBalancesResponse>(
        `api/leave/balances?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_balances) {
        setLeaveBalances(response.extend.leave_balances);
      } else {
        setLeaveBalances([]);
      }
    } catch (error: any) {
      console.error('Error fetching leave balances:', error);
      setError('Failed to fetch leave balances');
      setLeaveBalances([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) return;

      const response = await apiGet<EmployeesResponse>(
        `api/employees?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.employees) {
        setEmployees(response.employees);
      }
    } catch (error: any) {
      console.error('Error fetching employees:', error);
    }
  };

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) return;

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.error('Error fetching leave types:', error);
    }
  };

  useEffect(() => {
    fetchLeaveBalances();
    fetchEmployees();
    fetchLeaveTypes();
  }, [companies]);

  // Filter balances based on search and filters
  const filteredBalances = leaveBalances.filter(balance => {
    const matchesSearch = balance.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         balance.leave_type_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         balance.leave_type_code.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesYear = selectedYear === 0 || balance.year === selectedYear;
    const matchesEmployee = selectedEmployee === '' || balance.employee_id === selectedEmployee;
    const matchesLeaveType = selectedLeaveType === '' || balance.leave_type_id === selectedLeaveType;

    return matchesSearch && matchesYear && matchesEmployee && matchesLeaveType;
  });

  // Calculate statistics
  const totalBalances = filteredBalances.length;
  const totalDaysAllocated = filteredBalances.reduce((sum, balance) => sum + balance.total_days, 0);
  const totalDaysUsed = filteredBalances.reduce((sum, balance) => sum + balance.used_days, 0);
  const totalDaysAvailable = filteredBalances.reduce((sum, balance) => sum + balance.available_days, 0);
  const totalPendingDays = filteredBalances.reduce((sum, balance) => sum + balance.pending_days, 0);

  const stats = [
    { title: 'Total Balances', value: totalBalances.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Total Allocated', value: totalDaysAllocated.toString(), change: '', changeType: 'positive' as const },
    { title: 'Days Used', value: totalDaysUsed.toString(), change: '', changeType: 'negative' as const },
    { title: 'Days Available', value: totalDaysAvailable.toString(), change: '', changeType: 'positive' as const },
    { title: 'Pending Days', value: totalPendingDays.toString(), change: '', changeType: 'neutral' as const },
  ];

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'year' || name === 'total_days' || name === 'carried_over_days' 
        ? parseInt(value) || 0 
        : value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      if (!companies || companies.length === 0) {
        throw new Error('Company information not available');
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const payload = {
        company_id: companyId,
        employee_id: formData.employee_id,
        leave_type_id: formData.leave_type_id,
        year: formData.year,
        total_days: formData.total_days,
        carried_over_days: formData.carried_over_days
      };

      if (editingBalance) {
        // Update existing balance
        await apiPut(
          `api/leave/balances/${editingBalance.balance_id}`,
          payload,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
      } else {
        // Create new balance
        await apiPost(
          'api/leave/balances',
          payload,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
      }

      // Refresh the balances list
      await fetchLeaveBalances();
      handleCloseModal();
    } catch (error: any) {
      console.error('Error saving leave balance:', error);
      setError(error.message || 'Failed to save leave balance');
    } finally {
      setSubmitting(false);
    }
  };

  // Open modal for creating new balance
  const handleCreateNew = () => {
    setEditingBalance(null);
    setFormData({
      employee_id: '',
      leave_type_id: '',
      year: new Date().getFullYear(),
      total_days: 0,
      carried_over_days: 0
    });
    setShowModal(true);
    setError('');
  };

  // Open modal for editing balance
  const handleEdit = (balance: LeaveBalance) => {
    setEditingBalance(balance);
    setFormData({
      employee_id: balance.employee_id,
      leave_type_id: balance.leave_type_id,
      year: balance.year,
      total_days: balance.total_days,
      carried_over_days: balance.carried_over_days
    });
    setShowModal(true);
    setError('');
  };

  // Close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setEditingBalance(null);
    setError('');
  };

  // Handle delete balance
  const handleDelete = async (balance: LeaveBalance) => {
    if (!confirm(`Are you sure you want to delete the leave balance for ${balance.employee_name} - ${balance.leave_type_name} (${balance.year})?`)) {
      return;
    }

    try {
      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      await apiDelete(`api/leave/balances/${balance.balance_id}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Refresh the balances list
      await fetchLeaveBalances();
    } catch (error: any) {
      console.error('Error deleting leave balance:', error);
      setError('Failed to delete leave balance');
    }
  };

  return (
    <div className="space-y-6">
      {/* Import the table component */}
      <LeaveBalancesTable
        leaveBalances={filteredBalances}
        loading={loading}
        stats={stats}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedYear={selectedYear}
        setSelectedYear={setSelectedYear}
        selectedEmployee={selectedEmployee}
        setSelectedEmployee={setSelectedEmployee}
        selectedLeaveType={selectedLeaveType}
        setSelectedLeaveType={setSelectedLeaveType}
        employees={employees}
        leaveTypes={leaveTypes}
        onCreateNew={handleCreateNew}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Modal for Create/Edit Leave Balance */}
      <LeaveBalanceModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        formData={formData}
        onInputChange={handleInputChange}
        employees={employees}
        leaveTypes={leaveTypes}
        editingBalance={editingBalance}
        submitting={submitting}
        error={error}
      />
    </div>
  );
};

export default LeaveBalancesContent;
