'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

const LeaveManagementContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Leave Management</h1>
        <div>
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Leave Request
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Leave Management</span>
      </div>

      <DashboardCard title="Leave Management">
        <div className="py-8 text-center">
          <p className="text-secondary">Leave management features will be implemented in a future update.</p>
          <p className="text-secondary mt-2">This page will include leave requests, approvals, and leave balances.</p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default LeaveManagementContent;
