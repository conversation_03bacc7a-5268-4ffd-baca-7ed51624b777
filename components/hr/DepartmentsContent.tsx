'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import DepartmentModal from '@/components/department/DepartmentModal';
import Link from 'next/link';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface DepartmentResponse {
  departments: Department[];
  pagination: {
    has_next: boolean;
    has_prev: boolean;
    page: number;
    pages: number;
    per_page: number;
    total_count: number;
  };
  success: boolean;
}

const DepartmentsContent: React.FC = () => {
  const { companies } = useAuth();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);

  // Open create modal
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  // Open edit modal
  const openEditModal = (department: Department) => {
    setSelectedDepartment(department);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedDepartment(null);
    setIsEditModalOpen(false);
  };

  // Open delete confirmation
  const openDeleteConfirm = (department: Department) => {
    setDepartmentToDelete(department);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setDepartmentToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  // Fetch departments when component mounts or companies change
  useEffect(() => {
    fetchDepartments();
  }, [companies]);

  // Function to fetch departments from API
  const fetchDepartments = async () => {
    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const response = await apiGet<DepartmentResponse>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.departments) {
        setDepartments(response.departments);
      } else {
        // If API call succeeds but doesn't return the expected data
        console.warn('API returned unexpected data format');
        setDepartments([]);
      }
    } catch (error: any) {
      console.error('Error fetching departments:', error);
      setError(error.message || 'Failed to fetch departments');
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to delete a department
  const handleDeleteDepartment = async () => {
    if (!departmentToDelete) return;

    try {
      setIsLoading(true);

      const { apiDelete } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      await apiDelete(`api/departments/${departmentToDelete.department_id}?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Remove the deleted department from the state
      setDepartments(departments.filter(dept => dept.department_id !== departmentToDelete.department_id));

      // Close the confirmation dialog
      closeDeleteConfirm();
    } catch (error: any) {
      console.error('Error deleting department:', error);
      setError(error.message || 'Failed to delete department');
    } finally {
      setIsLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="space-y-6">
      {/* Create Department Modal */}
      <DepartmentModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchDepartments();
        }}
      />

      {/* Edit Department Modal */}
      <DepartmentModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchDepartments();
        }}
        department={selectedDepartment}
        isEditing={true}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={closeDeleteConfirm}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            {/* Modal panel */}
            <div
              className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
              onClick={e => e.stopPropagation()}
            >
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Department</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete the department "{departmentToDelete?.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeleteDepartment}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Department Management</h1>
        <div>
          <button
            className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center"
            onClick={openCreateModal}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Department
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Departments</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <DashboardCard title="Department List">
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading departments...</p>
          </div>
        ) : departments.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No departments found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Add your first department
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Department Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Created Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {departments.map((department) => (
                  <tr key={department.department_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-secondary-dark">{department.name}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-secondary line-clamp-2">{department.description || 'No description'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-secondary">{formatDate(department.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-primary hover:text-primary-dark mr-3"
                        onClick={() => openEditModal(department)}
                      >
                        Edit
                      </button>
                      <button
                        className="text-red-600 hover:text-red-800"
                        onClick={() => openDeleteConfirm(department)}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default DepartmentsContent;
