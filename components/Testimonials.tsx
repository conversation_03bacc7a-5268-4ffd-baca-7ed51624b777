import React from 'react';

const testimonials = [
  {
    quote:
      "KaziSync has completely transformed how we manage attendance. The biometric integration works flawlessly, and the reporting features give us insights we never had before.",
    author: "<PERSON>",
    position: "HR Director",
    company: "Tech Innovations Inc.",
  },
  {
    quote:
      "The leave management system has reduced our approval time by 70%. Our managers love the mobile app that lets them approve requests on the go.",
    author: "<PERSON>",
    position: "Operations Manager",
    company: "Global Solutions Ltd.",
  },
  {
    quote:
      "As a multi-location business, KaziSync has been invaluable for tracking attendance across all our branches. The customer support team is also incredibly responsive.",
    author: "<PERSON>",
    position: "CEO",
    company: "Retail Chain Group",
  },
];

const Testimonials = () => {
  return (
    <section className="section bg-background-dark">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            What Our Customers Say
          </h2>
          <p className="text-secondary text-lg">
            Trusted by organizations of all sizes across various industries.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="card p-8 hover:shadow-lg transition-shadow duration-300 group"
            >
              <div className="mb-6 text-primary">
                <svg
                  className="h-10 w-10 opacity-80"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>
              <p className="text-secondary-dark mb-6 italic">{testimonial.quote}</p>
              <div className="mt-auto">
                <p className="font-semibold text-secondary-dark">{testimonial.author}</p>
                <p className="text-secondary text-sm">
                  {testimonial.position}, {testimonial.company}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold text-secondary-dark mb-8">Trusted by Companies Worldwide</h3>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16">
            {/* Placeholder for company logos */}
            {[1, 2, 3, 4, 5].map((_, index) => (
              <div key={index} className="h-12 w-32 bg-white rounded-md shadow-sm flex items-center justify-center">
                <div className="text-secondary-light font-medium">Logo {index + 1}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
