"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { login, getDashboardPathByRole } from '@/lib/auth';
import { useAuth } from '@/contexts/AuthContext';

const LoginForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setUser, setCompanies } = useAuth();

  // Get username from URL query parameter if available
  const usernameFromQuery = searchParams.get('username') || '';

  const [formData, setFormData] = useState({
    username: usernameFromQuery,
    password: '',
    rememberMe: false,
  });

  // Update username if the query parameter changes
  useEffect(() => {
    if (usernameFromQuery) {
      setFormData(prevData => ({
        ...prevData,
        username: usernameFromQuery
      }));

      // Focus on the password field if username is pre-filled
      const passwordInput = document.getElementById('password');
      if (passwordInput) {
        passwordInput.focus();
      }
    }
  }, [usernameFromQuery]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (!formData.username.trim()) {
      setError('Username is required');
      setIsLoading(false);
      return;
    }

    if (!formData.password) {
      setError('Password is required');
      setIsLoading(false);
      return;
    }

    try {
      console.log('Submitting login form with:', {
        username: formData.username,
        password: formData.password.replace(/./g, '*') // Log masked password for security
      });

      // Call the login API with the form data
      const response = await login({
        username: formData.username,
        password: formData.password
      });

      console.log('Login response received:', response.authenticated);

      if (response.authenticated) {
        // Log successful authentication
        console.log('Login successful for user:', response.user.name);
        console.log('User role:', response.user.role);
        console.log('Companies:', response.companies);

        // Update the user and companies state in AuthContext
        setUser(response.user);
        setCompanies(response.companies || []);

        // Get the dashboard path based on the user's role
        const dashboardPath = getDashboardPathByRole(response.user.role);
        console.log('Redirecting to dashboard:', dashboardPath);

        // Prefetch the dashboard route before navigating
        router.prefetch(dashboardPath);

        // Use router.replace instead of push to avoid adding to history stack
        router.replace(dashboardPath);
      } else {
        setError('Authentication failed. Please check your credentials.');
      }
    } catch (err: any) {
      console.error('Login error details:', err);

      // Check if it's a network error
      if (err.message === 'Failed to fetch' || err.name === 'TypeError') {
        setError('Network error: Cannot connect to the authentication server. Please check your internet connection and try again.');
      } else {
        // Provide more specific error message if available
        setError(err.message || 'Invalid username or password. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <div>
        <label htmlFor="username" className="block text-sm font-medium text-secondary-dark mb-1">
          Username
          {usernameFromQuery && (
            <span className="ml-2 text-xs text-green-600 font-normal">
              (Pre-filled from registration)
            </span>
          )}
        </label>
        <input
          id="username"
          name="username"
          type="text"
          autoComplete="username"
          required
          value={formData.username}
          onChange={handleChange}
          className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
            usernameFromQuery ? 'border-green-300 bg-green-50' : 'border-gray-300'
          }`}
          placeholder="Enter your username"
          readOnly={!!usernameFromQuery} // Make it read-only if pre-filled
        />
        {usernameFromQuery && (
          <p className="mt-1 text-xs text-green-600">
            Your account was successfully created. Please enter your password to log in.
          </p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-secondary-dark mb-1">
          Password
        </label>
        <input
          id="password"
          name="password"
          type="password"
          autoComplete="current-password"
          required
          value={formData.password}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="••••••••"
        />
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="rememberMe"
            name="rememberMe"
            type="checkbox"
            checked={formData.rememberMe}
            onChange={handleChange}
            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
          />
          <label htmlFor="rememberMe" className="ml-2 block text-sm text-secondary">
            Remember me
          </label>
        </div>
      </div>

      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full btn-primary py-2 flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            </>
          ) : (
            'Sign in'
          )}
        </button>
      </div>
    </form>
  );
};

export default LoginForm;
